// Test script to simulate frontend API calls
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8081';

// Create an axios instance similar to the frontend
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

async function testFrontendAPIFlow() {
  console.log('🧪 Testing Frontend API Flow...\n');
  
  try {
    // Step 1: Test tenant context resolution
    console.log('1️⃣ Testing tenant resolution by subdomain:');
    try {
      const tenantResponse = await api.get('/api/tenants/by-subdomain/acme');
      console.log('✅ Tenant resolved:', tenantResponse.data);
      
      const tenantId = tenantResponse.data.tenantId;
      console.log(`📋 Using tenant ID: ${tenantId}\n`);
      
      // Step 2: Set tenant context header (simulating frontend)
      api.defaults.headers.common['X-Tenant-ID'] = tenantId;
      console.log('2️⃣ Set tenant context header:', api.defaults.headers.common['X-Tenant-ID']);
      
      // Step 3: Test lookup values API call
      console.log('\n3️⃣ Testing lookup values API call:');
      const lookupResponse = await api.get('/api/lookups');
      console.log('✅ Lookup values fetched successfully!');
      console.log('📊 Response structure:');
      
      const data = lookupResponse.data;
      Object.keys(data).forEach(category => {
        console.log(`   ${category}: ${data[category].length} items`);
        if (data[category].length > 0) {
          console.log(`      Example: ${JSON.stringify(data[category][0])}`);
        }
      });
      
      // Step 4: Test the exact format expected by frontend
      console.log('\n4️⃣ Verifying frontend compatibility:');
      const expectedFields = ['issueType', 'issueStatus', 'issuePriority', 'issueSeverity', 'environment'];
      const missingFields = expectedFields.filter(field => !data[field]);
      const extraFields = Object.keys(data).filter(field => !expectedFields.includes(field));
      
      if (missingFields.length > 0) {
        console.log('⚠️  Missing expected fields:', missingFields);
      }
      if (extraFields.length > 0) {
        console.log('ℹ️  Extra fields (not expected by frontend):', extraFields);
      }
      if (missingFields.length === 0 && extraFields.length === 0) {
        console.log('✅ All expected fields present and no extra fields');
      }
      
      // Step 5: Test individual field structure
      console.log('\n5️⃣ Testing field structure:');
      expectedFields.forEach(field => {
        if (data[field] && data[field].length > 0) {
          const item = data[field][0];
          const hasValue = item.hasOwnProperty('value');
          const hasLabel = item.hasOwnProperty('label');
          console.log(`   ${field}: value=${hasValue}, label=${hasLabel}`);
          if (!hasValue || !hasLabel) {
            console.log(`      ⚠️  Structure: ${JSON.stringify(item)}`);
          }
        }
      });
      
    } catch (error) {
      console.log('❌ Tenant resolution failed:', error.response?.status, error.response?.data);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testFrontendAPIFlow();
