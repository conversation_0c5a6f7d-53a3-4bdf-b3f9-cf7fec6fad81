#!/usr/bin/env node

/**
 * HTTPS Diagnosis Script
 * 
 * This script helps diagnose HTTPS issues and provides step-by-step guidance
 * for resolving browser connectivity problems.
 */

const https = require('https');
const http = require('http');

// Disable SSL verification for self-signed certificates
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const BACKEND_URL = 'https://localhost:8443';
const FRONTEND_URL = 'https://localhost:5174';

function testConnection(url, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔍 Testing ${description}...`);
    console.log(`   URL: ${url}`);
    
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const request = client.get(url, {
      rejectUnauthorized: false,
      timeout: 5000
    }, (response) => {
      let data = '';
      response.on('data', chunk => data += chunk);
      response.on('end', () => {
        console.log(`   ✅ Status: ${response.statusCode}`);
        console.log(`   ✅ Content-Type: ${response.headers['content-type']}`);
        console.log(`   ✅ Content-Length: ${data.length} bytes`);
        resolve({
          statusCode: response.statusCode,
          headers: response.headers,
          data: data.substring(0, 200) + (data.length > 200 ? '...' : '')
        });
      });
    });
    
    request.on('error', (error) => {
      console.log(`   ❌ Error: ${error.message}`);
      reject(error);
    });
    
    request.setTimeout(5000, () => {
      request.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function diagnoseHTTPS() {
  console.log('🔒 HTTPS Diagnosis Tool');
  console.log('========================');
  
  let backendOk = false;
  let frontendOk = false;
  let proxyOk = false;
  
  // Test Backend
  try {
    await testConnection(`${BACKEND_URL}/actuator/health`, 'Backend Health Check');
    backendOk = true;
  } catch (error) {
    console.log(`   💡 Backend may not be running or SSL misconfigured`);
  }
  
  // Test Frontend
  try {
    const result = await testConnection(`${FRONTEND_URL}/`, 'Frontend Home Page');
    frontendOk = true;
    if (result.data.includes('<div id="root">')) {
      console.log(`   ✅ React app structure detected`);
    }
  } catch (error) {
    console.log(`   💡 Frontend may not be running or SSL misconfigured`);
  }
  
  // Test API Proxy
  try {
    await testConnection(`${FRONTEND_URL}/api/actuator/health`, 'API Proxy');
    proxyOk = true;
  } catch (error) {
    console.log(`   💡 API proxy may not be configured correctly`);
  }
  
  console.log('\n📊 Diagnosis Results');
  console.log('====================');
  console.log(`Backend HTTPS:  ${backendOk ? '✅ Working' : '❌ Failed'}`);
  console.log(`Frontend HTTPS: ${frontendOk ? '✅ Working' : '❌ Failed'}`);
  console.log(`API Proxy:      ${proxyOk ? '✅ Working' : '❌ Failed'}`);
  
  if (backendOk && frontendOk && proxyOk) {
    console.log('\n🎉 HTTPS Configuration: WORKING!');
    console.log('\n🌐 Browser Testing Guide:');
    console.log('1. Open: https://localhost:5174/');
    console.log('2. If you see a security warning:');
    console.log('   • Click "Advanced" or "Show Details"');
    console.log('   • Click "Proceed to localhost (unsafe)"');
    console.log('   • This is normal for self-signed certificates');
    console.log('3. The React app should load normally');
    console.log('4. Check browser console (F12) for any JavaScript errors');
    
    console.log('\n🎙️ Voice Features Testing:');
    console.log('• Navigate to a page with voice features');
    console.log('• Grant microphone permission when prompted');
    console.log('• Voice recording should work over HTTPS');
    
  } else {
    console.log('\n🔧 Troubleshooting Steps:');
    
    if (!backendOk) {
      console.log('\n❌ Backend Issues:');
      console.log('• Check if backend is running: cd backend && mvn spring-boot:run');
      console.log('• Verify SSL certificates exist in backend/src/main/resources/certs/');
      console.log('• Check application.yml has SSL enabled on port 8443');
    }
    
    if (!frontendOk) {
      console.log('\n❌ Frontend Issues:');
      console.log('• Check if frontend is running: cd frontend && npm run dev -- --config vite.config.https.ts');
      console.log('• Verify SSL certificates exist in certs/ directory');
      console.log('• Check vite.config.https.ts configuration');
    }
    
    if (!proxyOk && frontendOk) {
      console.log('\n❌ Proxy Issues:');
      console.log('• Check vite.config.https.ts proxy configuration');
      console.log('• Verify backend URL in proxy target');
      console.log('• Ensure CORS is properly configured');
    }
  }
  
  console.log('\n🔍 Manual Testing Commands:');
  console.log(`curl -k ${BACKEND_URL}/actuator/health`);
  console.log(`curl -k ${FRONTEND_URL}/`);
  console.log(`curl -k ${FRONTEND_URL}/api/actuator/health`);
  
  return backendOk && frontendOk && proxyOk;
}

// Run diagnosis
diagnoseHTTPS()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('\n❌ Diagnosis failed:', error.message);
    process.exit(1);
  });
