#!/bin/bash

# Multitenant System Test Script
# This script tests the core multitenant functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:8081"
FRONTEND_URL="http://localhost:3000"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[FAIL]${NC} $1"
}

# Function to check if service is running
check_service() {
    local url=$1
    local service_name=$2
    
    if curl -s "$url" >/dev/null 2>&1; then
        print_success "$service_name is running"
        return 0
    else
        print_error "$service_name is not running"
        return 1
    fi
}

# Function to test API endpoint
test_api() {
    local method=$1
    local endpoint=$2
    local data=$3
    local headers=$4
    local expected_status=$5
    
    print_status "Testing $method $endpoint"
    
    local cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$headers" ]; then
        cmd="$cmd $headers"
    fi
    
    if [ ! -z "$data" ]; then
        cmd="$cmd -d '$data'"
    fi
    
    cmd="$cmd $BASE_URL$endpoint"
    
    local response=$(eval $cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        print_success "Status: $status_code"
        echo "$body"
        return 0
    else
        print_error "Expected: $expected_status, Got: $status_code"
        echo "$body"
        return 1
    fi
}

# Main test function
run_tests() {
    echo "🧪 Starting Multitenant System Tests..."
    echo ""
    
    # Check if services are running
    print_status "Checking services..."
    check_service "$BASE_URL/actuator/health" "Backend" || exit 1
    check_service "$FRONTEND_URL" "Frontend" || exit 1
    echo ""
    
    # Test 1: Register a new tenant
    print_status "Test 1: Register new tenant"
    TENANT_DATA='{
        "tenantId": "testcorp",
        "name": "Test Corporation",
        "subdomain": "testcorp",
        "adminEmail": "<EMAIL>",
        "adminFirstName": "Test",
        "adminLastName": "Admin",
        "adminPassword": "TestPass123!",
        "description": "Test tenant for automated testing",
        "maxUsers": 50,
        "maxStorageMb": 500,
        "subscriptionPlan": "BASIC"
    }'
    
    if test_api "POST" "/api/tenants/register" "$TENANT_DATA" "-H 'Content-Type: application/json'" "201"; then
        print_success "Tenant registration successful"
    else
        print_warning "Tenant may already exist, continuing..."
    fi
    echo ""
    
    # Test 2: Get tenant by tenant ID
    print_status "Test 2: Get tenant by tenant ID"
    if test_api "GET" "/api/tenants/by-tenant-id/testcorp" "" "" "200"; then
        print_success "Tenant retrieval successful"
    else
        print_error "Failed to retrieve tenant"
        exit 1
    fi
    echo ""
    
    # Test 3: Login to tenant
    print_status "Test 3: Login to tenant"
    LOGIN_DATA='{"username": "testcorp_admin", "password": "TestPass123!"}'
    
    LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/signin" \
        -H "Content-Type: application/json" \
        -H "X-Tenant-ID: testcorp" \
        -d "$LOGIN_DATA")
    
    if echo "$LOGIN_RESPONSE" | grep -q "token"; then
        print_success "Tenant login successful"
        TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
        echo "Token: ${TOKEN:0:50}..."
    else
        print_error "Tenant login failed"
        echo "$LOGIN_RESPONSE"
        exit 1
    fi
    echo ""
    
    # Test 4: Access tenant data
    print_status "Test 4: Access tenant-specific data"
    if test_api "GET" "/api/issues" "" "-H 'Authorization: Bearer $TOKEN' -H 'X-Tenant-ID: testcorp'" "200"; then
        print_success "Tenant data access successful"
    else
        print_error "Failed to access tenant data"
        exit 1
    fi
    echo ""
    
    # Test 5: Create issue in tenant
    print_status "Test 5: Create issue in tenant"
    ISSUE_DATA='{
        "title": "Test Issue for Multitenant",
        "description": "This is a test issue created by automated testing",
        "type": "Bug",
        "priority": "High",
        "severity": "Major",
        "environment": "Testing"
    }'
    
    if test_api "POST" "/api/issues" "$ISSUE_DATA" "-H 'Content-Type: application/json' -H 'Authorization: Bearer $TOKEN' -H 'X-Tenant-ID: testcorp'" "201"; then
        print_success "Issue creation successful"
    else
        print_error "Failed to create issue"
        exit 1
    fi
    echo ""
    
    # Test 6: Register another tenant for isolation testing
    print_status "Test 6: Register second tenant for isolation testing"
    TENANT2_DATA='{
        "tenantId": "company2",
        "name": "Company Two",
        "subdomain": "company2",
        "adminEmail": "<EMAIL>",
        "adminFirstName": "Admin",
        "adminLastName": "Two",
        "adminPassword": "AdminPass123!",
        "description": "Second test tenant for isolation testing"
    }'
    
    if test_api "POST" "/api/tenants/register" "$TENANT2_DATA" "-H 'Content-Type: application/json'" "201"; then
        print_success "Second tenant registration successful"
    else
        print_warning "Second tenant may already exist, continuing..."
    fi
    echo ""
    
    # Test 7: Login to second tenant
    print_status "Test 7: Login to second tenant"
    LOGIN2_DATA='{"username": "company2_admin", "password": "AdminPass123!"}'
    
    LOGIN2_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/signin" \
        -H "Content-Type: application/json" \
        -H "X-Tenant-ID: company2" \
        -d "$LOGIN2_DATA")
    
    if echo "$LOGIN2_RESPONSE" | grep -q "token"; then
        print_success "Second tenant login successful"
        TOKEN2=$(echo "$LOGIN2_RESPONSE" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
    else
        print_error "Second tenant login failed"
        echo "$LOGIN2_RESPONSE"
        exit 1
    fi
    echo ""
    
    # Test 8: Verify data isolation
    print_status "Test 8: Verify data isolation between tenants"
    
    # Get issues from first tenant
    TENANT1_ISSUES=$(curl -s -X GET "$BASE_URL/api/issues" \
        -H "Authorization: Bearer $TOKEN" \
        -H "X-Tenant-ID: testcorp")
    
    # Get issues from second tenant
    TENANT2_ISSUES=$(curl -s -X GET "$BASE_URL/api/issues" \
        -H "Authorization: Bearer $TOKEN2" \
        -H "X-Tenant-ID: company2")
    
    # Check if the test issue exists in first tenant but not in second
    if echo "$TENANT1_ISSUES" | grep -q "Test Issue for Multitenant"; then
        if ! echo "$TENANT2_ISSUES" | grep -q "Test Issue for Multitenant"; then
            print_success "Data isolation verified - tenants have separate data"
        else
            print_error "Data isolation failed - issue found in both tenants"
            exit 1
        fi
    else
        print_warning "Test issue not found in first tenant, isolation test inconclusive"
    fi
    echo ""
    
    # Test 9: Test invalid tenant access
    print_status "Test 9: Test invalid tenant access"
    if test_api "GET" "/api/issues" "" "-H 'Authorization: Bearer $TOKEN' -H 'X-Tenant-ID: nonexistent'" "404"; then
        print_success "Invalid tenant access properly blocked"
    else
        print_error "Invalid tenant access not properly blocked"
        exit 1
    fi
    echo ""
    
    # Test 10: Test missing tenant context
    print_status "Test 10: Test missing tenant context"
    if test_api "GET" "/api/issues" "" "-H 'Authorization: Bearer $TOKEN'" "400"; then
        print_success "Missing tenant context properly handled"
    else
        print_error "Missing tenant context not properly handled"
        exit 1
    fi
    echo ""
    
    # Test 11: Get tenant status
    print_status "Test 11: Get tenant status"
    if test_api "GET" "/api/auth/tenant-status" "" "-H 'Authorization: Bearer $TOKEN' -H 'X-Tenant-ID: testcorp'" "200"; then
        print_success "Tenant status retrieval successful"
    else
        print_error "Failed to get tenant status"
        exit 1
    fi
    echo ""
    
    # Summary
    echo "✅ All multitenant tests completed successfully!"
    echo ""
    echo "📊 Test Summary:"
    echo "  ✅ Tenant registration"
    echo "  ✅ Tenant authentication"
    echo "  ✅ Tenant data access"
    echo "  ✅ Data isolation between tenants"
    echo "  ✅ Invalid tenant blocking"
    echo "  ✅ Tenant context validation"
    echo ""
    echo "🎉 Multitenant system is working correctly!"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up test data..."
    
    # Note: In a production system, you might want to clean up test tenants
    # For now, we'll leave them for manual inspection
    print_warning "Test tenants (testcorp, company2) left for manual inspection"
    print_status "To clean up manually, use the tenant management API or dashboard"
}

# Handle script interruption
trap cleanup INT TERM

# Run the tests
run_tests

print_success "Multitenant testing completed successfully!"
