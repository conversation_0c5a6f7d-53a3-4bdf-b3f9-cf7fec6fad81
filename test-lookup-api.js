// Test script to verify lookup API is working
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8081';

async function testLookupAPI() {
  console.log('Testing Lookup API...');
  
  try {
    // Test without tenant context (should fail)
    console.log('\n1. Testing without tenant context (should fail):');
    try {
      const response = await axios.get(`${API_BASE_URL}/api/lookups`);
      console.log('❌ Unexpected success:', response.status);
    } catch (error) {
      console.log('✅ Expected failure:', error.response?.status, error.response?.data?.message);
    }
    
    // Test with tenant context (should succeed)
    console.log('\n2. Testing with tenant context (should succeed):');
    try {
      const response = await axios.get(`${API_BASE_URL}/api/lookups`, {
        headers: {
          'X-Tenant-ID': 'acmecorp',
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ Success:', response.status);
      console.log('Response data keys:', Object.keys(response.data));
      console.log('Issue types count:', response.data.issueType?.length || 0);
      console.log('Issue status count:', response.data.issueStatus?.length || 0);
      console.log('Environment count:', response.data.environment?.length || 0);
      console.log('Priority count:', response.data.issuePriority?.length || 0);
      console.log('Severity count:', response.data.issueSeverity?.length || 0);
    } catch (error) {
      console.log('❌ Unexpected failure:', error.response?.status, error.response?.data);
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Run the test
testLookupAPI();
