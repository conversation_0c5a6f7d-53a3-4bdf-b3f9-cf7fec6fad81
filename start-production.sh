#!/bin/bash

# Production Startup Script for Bug Tracking System
# This script starts the application with real database data

echo "🚀 Starting Bug Tracking System in Production Mode..."
echo "=================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a service is running
check_service() {
    local service_name=$1
    local port=$2
    local url=$3
    
    echo -e "${BLUE}Checking ${service_name}...${NC}"
    
    if curl -s "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ ${service_name} is running on port ${port}${NC}"
        return 0
    else
        echo -e "${RED}❌ ${service_name} is not running on port ${port}${NC}"
        return 1
    fi
}

# Function to wait for service to start
wait_for_service() {
    local service_name=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    echo -e "${YELLOW}Waiting for ${service_name} to start...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ ${service_name} is ready!${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}Attempt ${attempt}/${max_attempts} - ${service_name} not ready yet...${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ ${service_name} failed to start within expected time${NC}"
    return 1
}

# Step 1: Check Prerequisites
echo -e "\n${BLUE}Step 1: Checking Prerequisites...${NC}"

# Check if PostgreSQL is running
if ! check_service "PostgreSQL" "5432" "postgresql://localhost:5432"; then
    echo -e "${RED}Please start PostgreSQL first:${NC}"
    echo "  brew services start postgresql"
    echo "  # or"
    echo "  sudo systemctl start postgresql"
    exit 1
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo -e "${RED}Node.js is not installed. Please install Node.js first.${NC}"
    exit 1
fi

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo -e "${RED}Java is not installed. Please install Java 17+ first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ All prerequisites are met${NC}"

# Step 2: Test Database Connection
echo -e "\n${BLUE}Step 2: Testing Database Connection...${NC}"
if command -v node &> /dev/null; then
    if [ -f "test-database-connection.js" ]; then
        echo "Running database connection test..."
        node test-database-connection.js
    else
        echo -e "${YELLOW}Database connection test script not found, skipping...${NC}"
    fi
fi

# Step 3: Start Backend
echo -e "\n${BLUE}Step 3: Starting Backend (Spring Boot)...${NC}"

if [ ! -d "backend" ]; then
    echo -e "${RED}Backend directory not found!${NC}"
    exit 1
fi

cd backend

# Check if Maven wrapper exists
if [ -f "./mvnw" ]; then
    echo "Starting backend with Maven wrapper..."
    ./mvnw spring-boot:run &
    BACKEND_PID=$!
else
    echo -e "${RED}Maven wrapper not found in backend directory${NC}"
    exit 1
fi

cd ..

# Wait for backend to start
if ! wait_for_service "Backend API" "http://localhost:8081/api/actuator/health"; then
    echo -e "${RED}Backend failed to start. Check the logs above.${NC}"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# Step 4: Start Frontend
echo -e "\n${BLUE}Step 4: Starting Frontend (React + Vite)...${NC}"

if [ ! -d "frontend" ]; then
    echo -e "${RED}Frontend directory not found!${NC}"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

cd frontend

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Installing frontend dependencies..."
    npm install
fi

# Start frontend
echo "Starting frontend development server..."
npm run dev &
FRONTEND_PID=$!

cd ..

# Wait for frontend to start
if ! wait_for_service "Frontend" "http://localhost:5173"; then
    echo -e "${RED}Frontend failed to start. Check the logs above.${NC}"
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 1
fi

# Step 5: Final Status
echo -e "\n${GREEN}🎉 Bug Tracking System Started Successfully!${NC}"
echo "=================================================="
echo -e "${GREEN}✅ Backend API:${NC} http://localhost:8081"
echo -e "${GREEN}✅ Frontend App:${NC} http://localhost:5173"
echo -e "${GREEN}✅ API Health:${NC} http://localhost:8081/api/actuator/health"
echo -e "${GREEN}✅ Database:${NC} PostgreSQL on localhost:5432"
echo ""
echo -e "${YELLOW}📋 Configuration:${NC}"
echo "  - Mock API: DISABLED (using real database)"
echo "  - Authentication: JWT tokens"
echo "  - Database: PostgreSQL with bug_tracker schema"
echo ""
echo -e "${YELLOW}🔧 To stop the services:${NC}"
echo "  Press Ctrl+C to stop this script"
echo "  Or run: kill $BACKEND_PID $FRONTEND_PID"
echo ""
echo -e "${BLUE}📖 Access the application at: http://localhost:5173${NC}"

# Keep script running and handle cleanup
cleanup() {
    echo -e "\n${YELLOW}Shutting down services...${NC}"
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo -e "${GREEN}Services stopped.${NC}"
    exit 0
}

trap cleanup SIGINT SIGTERM

# Wait for user to stop
echo -e "\n${YELLOW}Press Ctrl+C to stop all services...${NC}"
wait
