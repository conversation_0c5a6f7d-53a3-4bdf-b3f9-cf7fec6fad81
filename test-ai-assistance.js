#!/usr/bin/env node

/**
 * Test script for AI-assisted issue creation functionality
 * This script tests the backend AI assistance endpoint
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8081';

async function testAIAssistance() {
    console.log('🤖 Testing AI-Assisted Issue Creation');
    console.log('=====================================\n');

    // Test data
    const testCases = [
        {
            name: "Simple Bug Report",
            data: {
                briefDescription: "Login button not working on mobile devices",
                context: "This happens only on iOS Safari, started after last update",
                useAdvancedAI: false
            }
        },
        {
            name: "Feature Request",
            data: {
                briefDescription: "Add dark mode to the application",
                context: "Users have been requesting this feature for better usability",
                useAdvancedAI: false
            }
        },
        {
            name: "Critical Bug",
            data: {
                briefDescription: "Database connection crashes the entire system",
                context: "Happens during peak hours, affects all users",
                useAdvancedAI: false
            }
        }
    ];

    for (const testCase of testCases) {
        console.log(`📝 Testing: ${testCase.name}`);
        console.log(`Input: "${testCase.data.briefDescription}"`);

        try {
            const response = await axios.post(`${BASE_URL}/api/issues/ai-assist`, testCase.data, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Tenant-ID': 'murthy1'  // Add tenant ID for multi-tenant support
                },
                timeout: 10000
            });

            console.log('✅ Success!');
            console.log('Generated Response:');
            console.log(`  Title: ${response.data.title}`);
            console.log(`  Type: ${response.data.type}`);
            console.log(`  Severity: ${response.data.severity}`);
            console.log(`  Priority: ${response.data.priority}`);
            console.log(`  Environment: ${response.data.environment}`);
            console.log(`  Confidence: ${Math.round(response.data.confidence * 100)}%`);
            console.log(`  Used Advanced AI: ${response.data.usedAdvancedAI}`);
            if (response.data.notes) {
                console.log(`  Notes: ${response.data.notes}`);
            }
            console.log('  Description Preview:');
            console.log(`    ${response.data.description.substring(0, 100)}...`);

        } catch (error) {
            if (error.response) {
                console.log(`❌ Error ${error.response.status}: ${error.response.data.message || error.response.statusText}`);
                if (error.response.status === 503) {
                    console.log('   💡 AI assistance might be disabled in configuration');
                }
            } else if (error.code === 'ECONNREFUSED') {
                console.log('❌ Connection refused - make sure the backend server is running on port 8081');
                break;
            } else {
                console.log(`❌ Error: ${error.message}`);
            }
        }

        console.log(''); // Empty line for readability
    }

    console.log('🏁 Test completed!');
    console.log('\n📋 To enable AI assistance:');
    console.log('1. Set ai.assist.enabled=true in application.yml');
    console.log('2. Optionally set OPENAI_API_KEY environment variable for advanced AI');
    console.log('3. Restart the backend server');
}

// Check if axios is available
try {
    require.resolve('axios');
} catch (e) {
    console.log('❌ axios is required to run this test');
    console.log('Install it with: npm install axios');
    process.exit(1);
}

// Run the test
testAIAssistance().catch(console.error);
