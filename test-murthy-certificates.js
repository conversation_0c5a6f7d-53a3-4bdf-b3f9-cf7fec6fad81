#!/usr/bin/env node

/**
 * Test script to verify Murthy certificates are working properly
 * Tests both backend and frontend HTTPS connectivity
 */

const https = require('https');
const fs = require('fs');

// Disable SSL verification for self-signed certificates
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

console.log('🔒 Testing Murthy SSL Certificates Setup');
console.log('==========================================\n');

// Test backend HTTPS
function testBackend() {
    return new Promise((resolve, reject) => {
        console.log('🔧 Testing Backend (Spring Boot) HTTPS...');

        const options = {
            hostname: 'localhost',
            port: 8443,
            path: '/actuator/health',
            method: 'GET',
            rejectUnauthorized: false
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log('✅ Backend HTTPS: SUCCESS');
                    console.log(`   Status: ${res.statusCode}`);
                    console.log(`   Response: ${data}`);
                    try {
                        const cert = res.socket.getPeerCertificate();
                        if (cert && cert.subject) {
                            console.log(`   Certificate: ${cert.subject.CN}`);
                        }
                    } catch (e) {
                        console.log('   Certificate: Available');
                    }
                    console.log('');
                    resolve(true);
                } else {
                    console.log(`❌ Backend HTTPS: FAILED (Status: ${res.statusCode})\n`);
                    resolve(false);
                }
            });
        });

        req.on('error', (error) => {
            console.log(`❌ Backend HTTPS: ERROR - ${error.message}\n`);
            resolve(false);
        });

        req.setTimeout(5000, () => {
            console.log('❌ Backend HTTPS: TIMEOUT\n');
            resolve(false);
        });

        req.end();
    });
}

// Test frontend HTTPS
function testFrontend() {
    return new Promise((resolve, reject) => {
        console.log('🌐 Testing Frontend (Vite) HTTPS...');

        const options = {
            hostname: 'localhost',
            port: 5173,
            path: '/',
            method: 'GET',
            rejectUnauthorized: false
        };

        const req = https.request(options, (res) => {
            if (res.statusCode === 200) {
                console.log('✅ Frontend HTTPS: SUCCESS');
                console.log(`   Status: ${res.statusCode}`);
                console.log(`   Content-Type: ${res.headers['content-type']}`);
                try {
                    const cert = res.socket.getPeerCertificate();
                    if (cert && cert.subject) {
                        console.log(`   Certificate: ${cert.subject.CN}`);
                    }
                } catch (e) {
                    console.log('   Certificate: Available');
                }
                console.log('');
                resolve(true);
            } else {
                console.log(`❌ Frontend HTTPS: FAILED (Status: ${res.statusCode})\n`);
                resolve(false);
            }
        });

        req.on('error', (error) => {
            console.log(`❌ Frontend HTTPS: ERROR - ${error.message}\n`);
            resolve(false);
        });

        req.setTimeout(5000, () => {
            console.log('❌ Frontend HTTPS: TIMEOUT\n');
            resolve(false);
        });

        req.end();
    });
}

// Test certificate files
function testCertificateFiles() {
    console.log('📁 Testing Certificate Files...');

    const files = [
        'certs/murthy.crt',
        'certs/murthy-ca.key',
        'certs/murthy.jks',
        'backend/src/main/resources/certs/murthy.jks'
    ];

    let allFilesExist = true;

    files.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`✅ ${file}: EXISTS`);
        } else {
            console.log(`❌ ${file}: MISSING`);
            allFilesExist = false;
        }
    });

    console.log('');
    return allFilesExist;
}

// Main test function
async function runTests() {
    const filesOk = testCertificateFiles();
    const backendOk = await testBackend();
    const frontendOk = await testFrontend();

    console.log('📊 Test Results Summary:');
    console.log('========================');
    console.log(`Certificate Files: ${filesOk ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Backend HTTPS:     ${backendOk ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Frontend HTTPS:    ${frontendOk ? '✅ PASS' : '❌ FAIL'}`);

    if (filesOk && backendOk && frontendOk) {
        console.log('\n🎉 All tests passed! Murthy certificates are working correctly.');
        console.log('\n🎙️ Voice Features Status:');
        console.log('   • HTTPS is properly configured');
        console.log('   • Microphone access will work in browsers');
        console.log('   • Speech recognition APIs are available');
        console.log('   • Text-to-speech functionality enabled');
        console.log('\n🌐 Access URLs:');
        console.log('   • Frontend: https://localhost:5173');
        console.log('   • Backend:  https://localhost:8443');
        console.log('   • Health:   https://localhost:8443/actuator/health');
    } else {
        console.log('\n❌ Some tests failed. Please check the configuration.');
        process.exit(1);
    }
}

// Run the tests
runTests().catch(console.error);
