const { Client } = require('./backend/node_modules/pg');

// Connection configuration
const client = new Client({
  connectionString: 'postgres://postgres:password@localhost:5432/rnd_app',
  schema: 'bug_tracker'
});

async function checkMigrations() {
  try {
    // Connect to the database
    await client.connect();
    console.log('Connected to PostgreSQL database');

    // Set the search path to the bug_tracker schema
    await client.query('SET search_path TO bug_tracker');
    console.log('Set search path to bug_tracker schema');

    // Check if new columns exist in issues table
    const columnsQuery = `
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_schema = 'bug_tracker'
      AND table_name = 'issues'
      AND column_name IN ('dev_completion_date', 'qc_completion_date', 'root_cause', 'target_date')
      ORDER BY column_name
    `;

    const columnsResult = await client.query(columnsQuery);
    console.log('\nColumns in issues table (new fields):');
    if (columnsResult.rows.length === 0) {
      console.log('❌ No new columns found - migrations may not have been applied');
    } else {
      columnsResult.rows.forEach(col => {
        console.log(`✅ ${col.column_name}: ${col.data_type}`);
      });
    }

    // Check lookup values for ISSUE_SEVERITY
    const severityQuery = `
      SELECT code, display_name, sort_order
      FROM lookup_values
      WHERE category = 'ISSUE_SEVERITY'
      ORDER BY sort_order
    `;

    const severityResult = await client.query(severityQuery);
    console.log('\nISSUE_SEVERITY lookup values:');
    if (severityResult.rows.length === 0) {
      console.log('❌ No severity values found');
    } else {
      severityResult.rows.forEach(val => {
        console.log(`- ${val.code}: ${val.display_name} (order: ${val.sort_order})`);
      });
    }

    // Check lookup values for ISSUE_PRIORITY
    const priorityQuery = `
      SELECT code, display_name, sort_order
      FROM lookup_values
      WHERE category = 'ISSUE_PRIORITY'
      ORDER BY sort_order
    `;

    const priorityResult = await client.query(priorityQuery);
    console.log('\nISSUE_PRIORITY lookup values:');
    if (priorityResult.rows.length === 0) {
      console.log('❌ No priority values found');
    } else {
      priorityResult.rows.forEach(val => {
        console.log(`- ${val.code}: ${val.display_name} (order: ${val.sort_order})`);
      });
    }

    // Check lookup values for ISSUE_STATUS
    const statusQuery = `
      SELECT code, display_name, sort_order
      FROM lookup_values
      WHERE category = 'ISSUE_STATUS'
      ORDER BY sort_order
    `;

    const statusResult = await client.query(statusQuery);
    console.log('\nISSUE_STATUS lookup values:');
    if (statusResult.rows.length === 0) {
      console.log('❌ No status values found');
    } else {
      statusResult.rows.forEach(val => {
        console.log(`- ${val.code}: ${val.display_name} (order: ${val.sort_order})`);
      });
    }

    // Check lookup values for ISSUE_ENVIRONMENT
    const environmentQuery = `
      SELECT code, display_name, sort_order
      FROM lookup_values
      WHERE category = 'ISSUE_ENVIRONMENT'
      ORDER BY sort_order
    `;

    const environmentResult = await client.query(environmentQuery);
    console.log('\nISSUE_ENVIRONMENT lookup values:');
    if (environmentResult.rows.length === 0) {
      console.log('❌ No environment values found');
    } else {
      environmentResult.rows.forEach(val => {
        console.log(`- ${val.code}: ${val.display_name} (order: ${val.sort_order})`);
      });
    }

    // Check Flyway migration history
    const migrationQuery = `
      SELECT version, description, installed_on, success
      FROM flyway_schema_history
      WHERE version LIKE 'V16%'
      ORDER BY installed_on DESC
    `;

    const migrationResult = await client.query(migrationQuery);
    console.log('\nFlyway migration history (V16):');
    if (migrationResult.rows.length === 0) {
      console.log('❌ V16 migration not found in history');
    } else {
      migrationResult.rows.forEach(migration => {
        console.log(`- Version: ${migration.version}, Success: ${migration.success}, Installed: ${migration.installed_on}`);
        console.log(`  Description: ${migration.description}`);
      });
    }

  } catch (error) {
    console.error('Error checking migrations:', error);
  } finally {
    // Close the connection
    await client.end();
    console.log('\nConnection closed');
  }
}

// Run the check
checkMigrations();
