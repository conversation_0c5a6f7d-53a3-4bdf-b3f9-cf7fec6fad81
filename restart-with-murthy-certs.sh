#!/bin/bash

# Restart services with Murthy certificates
# This script stops any running services and starts them with the new certificates

echo "🔒 Restarting Services with Murthy SSL Certificates"
echo "===================================================="

# Function to kill processes by name
kill_processes() {
    echo "🛑 Stopping existing services..."
    
    # Kill any running Spring Boot processes
    pkill -f "spring-boot:run" 2>/dev/null || true
    
    # Kill any running Vite processes
    pkill -f "vite" 2>/dev/null || true
    
    # Kill any running npm processes
    pkill -f "npm run dev" 2>/dev/null || true
    
    # Wait a moment for processes to stop
    sleep 3
    echo "   Services stopped"
}

# Function to check if certificates exist
check_certificates() {
    echo "📁 Checking Murthy certificates..."
    
    local missing_files=()
    
    if [ ! -f "certs/murthy.crt" ]; then
        missing_files+=("certs/murthy.crt")
    fi
    
    if [ ! -f "certs/murthy-ca.key" ]; then
        missing_files+=("certs/murthy-ca.key")
    fi
    
    if [ ! -f "certs/murthy.jks" ]; then
        missing_files+=("certs/murthy.jks")
    fi
    
    if [ ! -f "backend/src/main/resources/certs/murthy.jks" ]; then
        missing_files+=("backend/src/main/resources/certs/murthy.jks")
    fi
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        echo "❌ Missing certificate files:"
        for file in "${missing_files[@]}"; do
            echo "   - $file"
        done
        echo ""
        echo "Please ensure all Murthy certificate files are in place."
        echo "Run the certificate setup process first."
        exit 1
    fi
    
    echo "✅ All certificate files found"
}

# Function to start backend
start_backend() {
    echo ""
    echo "🚀 Starting Backend with Murthy certificates..."
    cd backend
    
    # Set Java home
    export JAVA_HOME="/usr/local/opt/openjdk@21/libexec/openjdk.jdk/Contents/Home"
    
    # Start backend in background
    nohup mvn spring-boot:run > ../logs/backend-murthy.log 2>&1 &
    BACKEND_PID=$!
    
    cd ..
    
    echo "   Backend starting (PID: $BACKEND_PID)..."
    echo "   Logs: logs/backend-murthy.log"
    
    # Wait for backend to start
    echo "⏳ Waiting for backend to initialize..."
    sleep 15
    
    # Check if backend is responding
    for i in {1..6}; do
        if curl -k -s https://localhost:8443/actuator/health > /dev/null 2>&1; then
            echo "✅ Backend started successfully on https://localhost:8443"
            return 0
        elif [ $i -eq 6 ]; then
            echo "❌ Backend failed to start. Check logs/backend-murthy.log"
            tail -10 logs/backend-murthy.log
            return 1
        else
            echo "   Attempt $i/6 - waiting 5 more seconds..."
            sleep 5
        fi
    done
}

# Function to start frontend
start_frontend() {
    echo ""
    echo "🌐 Starting Frontend with Murthy certificates..."
    cd frontend
    
    # Start frontend in background
    nohup npm run dev -- --config vite.config.https.ts > ../logs/frontend-murthy.log 2>&1 &
    FRONTEND_PID=$!
    
    cd ..
    
    echo "   Frontend starting (PID: $FRONTEND_PID)..."
    echo "   Logs: logs/frontend-murthy.log"
    
    # Wait for frontend to start
    echo "⏳ Waiting for frontend to initialize..."
    sleep 8
    
    # Check if frontend is responding
    if curl -k -s -o /dev/null -w "%{http_code}" https://localhost:5173/ | grep -q "200"; then
        echo "✅ Frontend started successfully on https://localhost:5173"
        return 0
    else
        echo "❌ Frontend may have issues. Check logs/frontend-murthy.log"
        return 1
    fi
}

# Function to display final status
show_status() {
    echo ""
    echo "🎉 Services Restarted with Murthy Certificates!"
    echo "==============================================="
    echo ""
    echo "🌐 Access URLs:"
    echo "   • Frontend:  https://localhost:5173"
    echo "   • Backend:   https://localhost:8443"
    echo "   • Health:    https://localhost:8443/actuator/health"
    echo ""
    echo "🎙️ Voice Features:"
    echo "   • HTTPS enabled with custom Murthy certificates"
    echo "   • Microphone access available in browsers"
    echo "   • Speech recognition and TTS ready"
    echo ""
    echo "⚠️  Browser Security:"
    echo "   • Accept certificate warnings when prompted"
    echo "   • Grant microphone permissions for voice features"
    echo ""
    echo "📋 Logs:"
    echo "   • Backend:  logs/backend-murthy.log"
    echo "   • Frontend: logs/frontend-murthy.log"
    echo ""
    echo "🛑 To stop services: pkill -f 'spring-boot:run' && pkill -f 'vite'"
}

# Main execution
main() {
    # Create logs directory
    mkdir -p logs
    
    # Stop existing services
    kill_processes
    
    # Check certificates
    check_certificates
    
    # Start backend
    if ! start_backend; then
        echo "❌ Failed to start backend. Exiting."
        exit 1
    fi
    
    # Start frontend
    if ! start_frontend; then
        echo "⚠️  Frontend may have issues, but backend is running."
    fi
    
    # Show final status
    show_status
}

# Run main function
main
