#!/bin/bash

# Start HTTPS Development Environment
# This script starts both backend and frontend with HTTPS enabled

echo "🔒 Starting HTTPS Development Environment for Voice-Powered AI Assistant"
echo "========================================================================="

# Check if SSL certificates exist
if [ ! -f "certs/murthy.crt" ] || [ ! -f "certs/murthy-ca.key" ]; then
    echo "❌ Murthy SSL certificates not found!"
    echo "Please ensure the following files exist in certs/ directory:"
    echo "  - murthy.crt (certificate file)"
    echo "  - murthy-ca.key (private key file)"
    echo "  - murthy.jks (Java keystore for backend)"
    exit 1
fi

echo "✅ SSL certificates found"

# Function to kill background processes on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo "   Backend stopped"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo "   Frontend stopped"
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start Backend with HTTPS
echo ""
echo "🚀 Starting Backend (Spring Boot) with HTTPS on port 8443..."
cd backend
export JAVA_HOME="/usr/local/opt/openjdk@21/libexec/openjdk.jdk/Contents/Home"
mvn spring-boot:run > ../logs/backend-https.log 2>&1 &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 10

# Check if backend is running (with retries)
echo "🔍 Checking backend health..."
for i in {1..6}; do
    if curl -k -s https://localhost:8443/api/actuator/health > /dev/null 2>&1; then
        echo "✅ Backend health check passed"
        break
    elif [ $i -eq 6 ]; then
        echo "❌ Backend failed to start after 30 seconds. Check logs/backend-https.log"
        echo "📋 Last few lines of backend log:"
        tail -10 logs/backend-https.log
        cleanup
        exit 1
    else
        echo "   Attempt $i/6 - waiting 5 more seconds..."
        sleep 5
    fi
done

echo "✅ Backend started successfully on https://localhost:8443"

# Start Frontend with HTTPS
echo ""
echo "🚀 Starting Frontend (Vite) with HTTPS on port 5173..."
cd frontend
npm run dev > ../logs/frontend-https.log 2>&1 &
FRONTEND_PID=$!
cd ..

# Wait for frontend to start
echo "⏳ Waiting for frontend to start..."
sleep 5

echo ""
echo "🎉 HTTPS Development Environment Started Successfully!"
echo "====================================================="
echo ""
echo "🌐 Frontend: https://localhost:5173"
echo "🔧 Backend:  https://localhost:8443"
echo "📊 Health:   https://localhost:8443/api/actuator/health"
echo ""
echo "🎙️ Voice Features Ready!"
echo "   • Microphone access will work properly over HTTPS"
echo "   • Speech recognition APIs are available"
echo "   • Text-to-speech functionality enabled"
echo ""
echo "⚠️  Browser Security Notice:"
echo "   • You may see a security warning for self-signed certificates"
echo "   • Click 'Advanced' → 'Proceed to localhost (unsafe)' to continue"
echo "   • Or add the certificate to your system's trusted certificates"
echo ""
echo "🔧 Logs:"
echo "   • Backend:  logs/backend-https.log"
echo "   • Frontend: logs/frontend-https.log"
echo ""
echo "Press Ctrl+C to stop all services"

# Create logs directory if it doesn't exist
mkdir -p logs

# Wait for user to stop services
wait $BACKEND_PID $FRONTEND_PID
