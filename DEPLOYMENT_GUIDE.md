# Database-Agnostic Refactoring Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the database-agnostic refactoring of the multitenant bug tracking application.

## Pre-Deployment Checklist

### 1. Code Validation ✅
- [x] All native SQL queries converted to JPA
- [x] PostgreSQL-specific syntax removed
- [x] Compilation successful
- [x] Unit tests passing
- [x] Multitenant architecture preserved

### 2. Database Compatibility
The refactored application now supports:
- **PostgreSQL** (existing)
- **MySQL** (new)
- **MariaDB** (new)
- **Oracle** (new)
- **SQL Server** (new)
- **H2** (for testing)

## Deployment Steps

### Step 1: Backup Current System
```bash
# Backup database
pg_dump -h localhost -U postgres -d rnd_app > backup_$(date +%Y%m%d_%H%M%S).sql

# Backup application code
tar -czf app_backup_$(date +%Y%m%d_%H%M%S).tar.gz /path/to/application
```

### Step 2: Deploy New Code
```bash
# Pull latest changes
git pull origin main

# Build application
cd backend
export JAVA_HOME="/usr/local/opt/openjdk@21/libexec/openjdk.jdk/Contents/Home"
./mvnw clean package -DskipTests

# Deploy to server
# (Copy target/bug-tracking-system-*.jar to deployment location)
```

### Step 3: Configuration Updates
No configuration changes required - the application uses the same database connection settings.

### Step 4: Restart Application
```bash
# Stop current application
sudo systemctl stop bug-tracker

# Start new version
sudo systemctl start bug-tracker

# Verify startup
sudo systemctl status bug-tracker
tail -f /var/log/bug-tracker/application.log
```

### Step 5: Validation Testing
```bash
# Run health check
curl http://localhost:8081/actuator/health

# Test lookup values endpoint
curl http://localhost:8081/api/lookups/all

# Test issue creation
curl -X POST http://localhost:8081/api/issues \
  -H "Content-Type: application/json" \
  -d '{"title":"Test Issue","description":"Test","type":"BUG","priority":"MEDIUM"}'
```

## Rollback Plan

If issues are encountered:

### Quick Rollback
```bash
# Stop new version
sudo systemctl stop bug-tracker

# Restore previous version
cp /backup/bug-tracking-system-old.jar /opt/bug-tracker/
sudo systemctl start bug-tracker
```

### Database Rollback
No database changes were made, so no database rollback is needed.

## Performance Monitoring

### Key Metrics to Monitor
1. **Response Times**
   - Lookup value queries
   - Issue creation/retrieval
   - Dashboard statistics

2. **Database Connections**
   - Connection pool utilization
   - Query execution times
   - Lock contention

3. **Memory Usage**
   - JPA entity cache
   - Connection pool memory
   - Overall heap usage

### Monitoring Commands
```bash
# Check application logs
tail -f /var/log/bug-tracker/application.log | grep -E "(ERROR|WARN|JPA|SQL)"

# Monitor database connections
psql -d rnd_app -c "SELECT count(*) FROM pg_stat_activity WHERE datname='rnd_app';"

# Check JVM metrics
curl http://localhost:8081/actuator/metrics/jvm.memory.used
```

## Testing Different Databases

### MySQL Setup
```yaml
# application-mysql.yml
spring:
  datasource:
    url: ***************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    database-platform: org.hibernate.dialect.MySQLDialect
```

### H2 Testing Setup
```yaml
# application-test.yml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
```

## Troubleshooting

### Common Issues

#### 1. JPA Query Performance
**Symptom**: Slower response times
**Solution**: 
- Enable query logging: `logging.level.org.hibernate.SQL=DEBUG`
- Check for N+1 query problems
- Add appropriate indexes

#### 2. Connection Pool Issues
**Symptom**: Connection timeout errors
**Solution**:
- Increase pool size: `spring.datasource.hikari.maximum-pool-size=20`
- Check connection leaks
- Monitor pool metrics

#### 3. Tenant Context Issues
**Symptom**: Cross-tenant data access
**Solution**:
- Verify TenantInterceptor is working
- Check tenant context in logs
- Validate schema switching

### Debug Commands
```bash
# Enable debug logging
export JAVA_OPTS="-Dlogging.level.com.bugtracker=DEBUG"

# Check tenant schemas
psql -d rnd_app -c "\dn tenant_*"

# Verify JPA queries
grep "Hibernate:" /var/log/bug-tracker/application.log
```

## Success Criteria

### Functional Validation
- [ ] All existing features work correctly
- [ ] Lookup values load properly
- [ ] Issue creation generates correct identifiers
- [ ] Dashboard statistics display accurately
- [ ] Multitenant data isolation maintained

### Performance Validation
- [ ] Response times within 10% of baseline
- [ ] No memory leaks detected
- [ ] Database connection pool stable
- [ ] No query performance degradation

### Security Validation
- [ ] No cross-tenant data access
- [ ] Authentication/authorization working
- [ ] Tenant context properly maintained
- [ ] No SQL injection vulnerabilities

## Post-Deployment Tasks

### 1. Documentation Updates
- Update API documentation
- Update deployment procedures
- Document new database support

### 2. Monitoring Setup
- Configure alerts for performance metrics
- Set up database monitoring
- Monitor application logs

### 3. Team Training
- Brief team on JPA changes
- Update development guidelines
- Share troubleshooting procedures

## Contact Information

For deployment issues:
- **Technical Lead**: [Your Name]
- **Database Admin**: [DBA Name]
- **DevOps Team**: [DevOps Contact]

## Appendix

### Files Modified
- `LookupServiceImpl.java` - Converted to JPA queries
- `IssueServiceImpl.java` - Converted counter logic to JPA
- `LookupValueRepository.java` - Added new query methods
- `SimpleTenantDataSourceConfig.java` - Removed PostgreSQL-specific code
- `LookupService.java` - Updated interface
- `LookupController.java` - Updated method calls

### Test Coverage
- Unit tests: 7 tests covering all converted methods
- Integration tests: Multitenant data isolation verified
- Performance tests: Recommended for production deployment

### Database Compatibility Matrix
| Database | Version | Status | Notes |
|----------|---------|--------|-------|
| PostgreSQL | 12+ | ✅ Tested | Original database |
| MySQL | 8.0+ | ✅ Compatible | Requires driver update |
| MariaDB | 10.5+ | ✅ Compatible | MySQL compatible |
| Oracle | 19c+ | ⚠️ Untested | Should work with driver |
| SQL Server | 2019+ | ⚠️ Untested | Should work with driver |
| H2 | 2.0+ | ✅ Tested | For testing only |
