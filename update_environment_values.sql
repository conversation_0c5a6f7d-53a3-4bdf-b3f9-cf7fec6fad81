-- Update existing issues to have proper environment values
-- This script updates any issues that might have old environment values

-- Update DEV to DEVELOPMENT
UPDATE bug_tracker.issues 
SET environment = 'DEVELOPMENT' 
WHERE environment = 'DEV';

-- Update QC to TESTING
UPDATE bug_tracker.issues 
SET environment = 'TESTING' 
WHERE environment = 'QC';

-- Ensure all issues have a valid environment value
UPDATE bug_tracker.issues 
SET environment = 'DEVELOPMENT' 
WHERE environment IS NULL OR environment = '';

-- Show current environment distribution
SELECT environment, COUNT(*) as count 
FROM bug_tracker.issues 
GROUP BY environment 
ORDER BY environment;
