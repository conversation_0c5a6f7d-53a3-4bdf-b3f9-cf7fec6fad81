const axios = require('./node_modules/axios');

// Configure axios to ignore SSL certificate errors for testing
const api = axios.create({
  baseURL: 'https://localhost:8443/api',
  headers: {
    'Content-Type': 'application/json',
    'X-Tenant-ID': 'murthy1'
  },
  httpsAgent: new (require('https').Agent)({
    rejectUnauthorized: false
  })
});

async function testBackendValidation() {
  console.log('🧪 Testing Backend Validation...\n');

  try {
    // First, let's authenticate to get a valid token
    console.log('1. Authenticating...');
    const authResponse = await api.post('/auth/login', {
      username: 'admin',
      password: 'admin123'
    });
    
    const token = authResponse.data.token;
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    console.log('✅ Authentication successful\n');

    // Test 1: Create issue with Dev Completed status but no root cause (should fail)
    console.log('2. Testing Root Cause validation...');
    try {
      await api.post('/issues', {
        title: 'Test Issue - Root Cause Validation',
        description: 'Testing root cause requirement',
        type: 'BUG',
        severity: 'HIGH',
        priority: 'HIGH',
        status: 'DEV_COMPLETED',
        environment: 'DEVELOPMENT',
        // Missing rootCause - should fail
      });
      console.log('❌ Root cause validation failed - issue created without root cause');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Root cause validation working - correctly rejected issue without root cause');
      } else {
        console.log('⚠️ Unexpected error:', error.message);
      }
    }

    // Test 2: Create issue with valid root cause (should succeed)
    console.log('\n3. Testing valid issue creation with root cause...');
    try {
      const response = await api.post('/issues', {
        title: 'Test Issue - Valid Root Cause',
        description: 'Testing valid root cause',
        type: 'BUG',
        severity: 'HIGH',
        priority: 'HIGH',
        status: 'DEV_COMPLETED',
        environment: 'DEVELOPMENT',
        rootCause: 'This is a valid root cause explanation'
      });
      console.log('✅ Issue created successfully with root cause, ID:', response.data.id);
      
      // Test 3: Test date validation - QC date before Dev date (should fail)
      console.log('\n4. Testing date validation...');
      try {
        await api.put(`/issues/${response.data.id}`, {
          title: 'Test Issue - Date Validation',
          description: 'Testing date validation',
          type: 'BUG',
          severity: 'HIGH',
          priority: 'HIGH',
          status: 'QC_COMPLETED',
          environment: 'DEVELOPMENT',
          rootCause: 'This is a valid root cause explanation',
          devCompletionDate: '2024-01-15',
          qcCompletionDate: '2024-01-10' // Before dev completion date - should fail
        });
        console.log('❌ Date validation failed - QC date allowed before Dev date');
      } catch (error) {
        if (error.response && error.response.status === 400) {
          console.log('✅ Date validation working - correctly rejected QC date before Dev date');
        } else {
          console.log('⚠️ Unexpected error:', error.message);
        }
      }

      // Test 4: Test valid date order (should succeed)
      console.log('\n5. Testing valid date order...');
      try {
        await api.put(`/issues/${response.data.id}`, {
          title: 'Test Issue - Valid Dates',
          description: 'Testing valid date order',
          type: 'BUG',
          severity: 'HIGH',
          priority: 'HIGH',
          status: 'QC_COMPLETED',
          environment: 'DEVELOPMENT',
          rootCause: 'This is a valid root cause explanation',
          devCompletionDate: '2024-01-10',
          qcCompletionDate: '2024-01-15' // After dev completion date - should succeed
        });
        console.log('✅ Valid date order accepted successfully');
      } catch (error) {
        console.log('❌ Valid date order rejected:', error.response?.data?.message || error.message);
      }

    } catch (error) {
      console.log('❌ Failed to create test issue:', error.response?.data?.message || error.message);
    }

    // Test 5: Test Critical severity auto-logic
    console.log('\n6. Testing Critical severity auto-logic...');
    try {
      const response = await api.post('/issues', {
        title: 'Test Issue - Critical Severity',
        description: 'Testing critical severity auto-logic',
        type: 'BUG',
        severity: 'CRITICAL',
        priority: 'LOW', // Should be auto-changed to CRITICAL
        status: 'NEW',
        environment: 'PRODUCTION'
      });
      
      // Fetch the created issue to verify auto-logic
      const issueResponse = await api.get(`/issues/${response.data.id}`);
      const issue = issueResponse.data;
      
      if (issue.priority === 'CRITICAL') {
        console.log('✅ Critical severity auto-logic working - priority auto-set to Critical');
      } else {
        console.log('❌ Critical severity auto-logic failed - priority not auto-set');
      }
      
      if (issue.devCompletionDate) {
        console.log('✅ Critical severity auto-logic working - dev completion date auto-set');
      } else {
        console.log('❌ Critical severity auto-logic failed - dev completion date not auto-set');
      }
      
    } catch (error) {
      console.log('❌ Failed to test critical severity:', error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.log('❌ Authentication failed:', error.response?.data?.message || error.message);
  }

  console.log('\n🏁 Backend validation testing completed!');
}

// Run the tests
testBackendValidation();
