# Database Setup Instructions

This document provides instructions for setting up the PostgreSQL database for the Issue Tracker application.

## Prerequisites

- PostgreSQL installed on your system (version 12 or higher recommended)
- PostgreSQL command-line tools (psql)
- Node.js and npm

## Setup Steps

### 1. Create the Database

```bash
# Connect to PostgreSQL as the postgres user
psql -U postgres

# Create the database
CREATE DATABASE issue_tracker;

# Connect to the new database
\c issue_tracker

# Exit psql
\q
```

### 2. Configure Environment Variables

Make sure your environment variables are properly set in the `.env` file in the backend directory:

```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=issue_tracker
DB_USER=postgres
DB_PASSWORD=your_password
```

Replace `your_password` with your actual PostgreSQL password.

### 3. Run Database Migrations

```bash
# Navigate to the backend directory
cd backend

# Install dependencies if you haven't already
npm install

# Run migrations
npx sequelize-cli db:migrate

# Seed the database with initial data (optional)
npx sequelize-cli db:seed:all
```

## Troubleshooting

### Connection Issues

If you encounter connection issues, check the following:

1. Ensure PostgreSQL is running
2. Verify your username and password are correct
3. Check that the database exists
4. Make sure your firewall allows connections to the PostgreSQL port

### Migration Issues

If migrations fail:

1. Check the migration logs for specific errors
2. Ensure your database user has sufficient privileges
3. Try running migrations one by one to identify the problematic migration

## Database Schema

The database schema includes the following main tables:

- `users` - User accounts
- `issues` - Bug tracking issues
- `comments` - Comments on issues
- `attachments` - File attachments for issues
- `watchers` - Users watching specific issues
- `issue_history` - History of changes to issues

## Backup and Restore

### Creating a Backup

```bash
pg_dump -U postgres -d issue_tracker > backup.sql
```

### Restoring from Backup

```bash
psql -U postgres -d issue_tracker < backup.sql
```
