# Bug Tracking System

A comprehensive Bug Tracking System with requirement management capabilities. The system provides a modern, responsive interface for tracking issues, managing requirements, and monitoring team performance through detailed analytics and reporting.

## Features

- **Issue & Requirement Management**: Track bugs and requirements with unique identifiers
- **User Interface**: Responsive UI with multiple view options (Kanban, <PERSON>, Calendar)
- **Dashboard & Analytics**: Dynamic dashboards showing various metrics
- **AI Enhancement**: AI-assisted issue description formatting
- **Notification System**: Comprehensive notification framework
- **User & Access Management**: Role-based access control

## Technology Stack

### Backend
- Java 21
- Spring Boot
- Spring Security
- PostgreSQL
- Flyway for database migrations

### Frontend
- React with TypeScript
- Material-UI
- Redux for state management
- Formik for form handling
- React Router for navigation

## Getting Started

### Prerequisites
- Java 21
- Node.js 18+
- Docker and Docker Compose (optional)

### Running with Docker
1. Clone the repository
2. Run `docker-compose up -d`
3. Access the application at http://localhost:3000

### Running Locally
#### Backend
1. Navigate to the `backend` directory
2. Run `mvn spring-boot:run`

#### Frontend
1. Navigate to the `frontend` directory
2. Run `npm install`
3. Run `npm run dev`
4. Access the application at http://localhost:3000

## Project Structure

### Backend
- `src/main/java/com/bugtracker/model`: Domain models
- `src/main/java/com/bugtracker/repository`: Data repositories
- `src/main/java/com/bugtracker/controller`: REST controllers
- `src/main/java/com/bugtracker/service`: Business logic
- `src/main/java/com/bugtracker/security`: Security configuration

### Frontend
- `src/components`: Reusable UI components
- `src/pages`: Page components
- `src/services`: API services
- `src/store`: Redux store configuration
- `src/utils`: Utility functions

## API Documentation

The API documentation is available at http://localhost:8081/swagger-ui.html when the backend is running.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
