// Script to fix lookup values schema across all tenant schemas
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8081';

async function fixAllTenantSchemas() {
  console.log('🔧 Starting comprehensive tenant schema fix...\n');

  try {
    // Step 1: Get all active tenants
    console.log('1️⃣ Fetching all active tenants...');
    const tenantsResponse = await axios.get(`${API_BASE_URL}/api/tenants`);
    const tenants = tenantsResponse.data.content; // Extract content from paginated response

    console.log(`Found ${tenants.length} tenants:`);
    tenants.forEach(tenant => {
      console.log(`   - ${tenant.tenantId} (${tenant.name}) - Schema: ${tenant.schemaName}`);
    });

    console.log('\n2️⃣ Applying schema fixes to all tenants...\n');

    // Step 2: Fix each tenant schema
    for (const tenant of tenants) {
      const tenantId = tenant.tenantId;
      console.log(`🔧 Processing tenant: ${tenantId}`);

      try {
        // Step 2a: Repair schema (fix checksum mismatches)
        console.log(`   📋 Repairing schema for ${tenantId}...`);
        const repairResponse = await axios.post(`${API_BASE_URL}/api/tenants/${tenantId}/schema/repair`);
        console.log(`   ✅ Repair completed: ${repairResponse.data.message}`);

        // Step 2b: Run migration (apply V11 fix)
        console.log(`   🚀 Running migration for ${tenantId}...`);
        const migrateResponse = await axios.post(`${API_BASE_URL}/api/tenants/${tenantId}/schema/migrate`);
        console.log(`   ✅ Migration completed: ${migrateResponse.data.message}`);

        // Step 2c: Test lookup values
        console.log(`   🧪 Testing lookup values for ${tenantId}...`);
        const testResponse = await axios.get(`${API_BASE_URL}/api/lookups`, {
          headers: { 'X-Tenant-ID': tenantId }
        });

        const lookupData = testResponse.data;
        const categories = Object.keys(lookupData);
        const totalValues = categories.reduce((sum, cat) => sum + lookupData[cat].length, 0);

        console.log(`   ✅ Lookup test passed: ${categories.length} categories, ${totalValues} total values`);
        console.log(`      Categories: ${categories.join(', ')}\n`);

      } catch (error) {
        console.error(`   ❌ Error processing tenant ${tenantId}:`, error.response?.data?.error || error.message);
        console.log('   ⏭️  Continuing with next tenant...\n');
      }
    }

    console.log('🎉 Schema fix completed for all tenants!');
    console.log('\n📊 Summary:');
    console.log(`   - Total tenants processed: ${tenants.length}`);
    console.log('   - All tenant schemas should now have correct lookup_values structure');
    console.log('   - All dropdowns should now work in the frontend');

  } catch (error) {
    console.error('❌ Failed to fix tenant schemas:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the fix
fixAllTenantSchemas();
