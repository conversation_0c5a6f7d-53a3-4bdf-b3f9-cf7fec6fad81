#!/bin/bash

# Generate SSL Certificates for HTTPS Development
# This script creates self-signed certificates for local development

echo "🔒 Generating SSL certificates for HTTPS development..."

# Create certs directory
mkdir -p certs

# Generate private key
openssl genrsa -out certs/localhost.key 2048

# Generate certificate signing request
openssl req -new -key certs/localhost.key -out certs/localhost.csr -subj "/C=US/ST=CA/L=San Francisco/O=Bug Tracker/OU=Development/CN=localhost"

# Create config file for certificate with SAN
cat > certs/localhost.conf << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = US
ST = CA
L = San Francisco
O = Bug Tracker
OU = Development
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = 127.0.0.1
DNS.3 = ::1
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Generate self-signed certificate
openssl x509 -req -in certs/localhost.csr -signkey certs/localhost.key -out certs/localhost.crt -days 365 -extensions v3_req -extfile certs/localhost.conf

# Generate certificate for backend (Java keystore format)
openssl pkcs12 -export -in certs/localhost.crt -inkey certs/localhost.key -out certs/localhost.p12 -name localhost -password pass:changeit

# Create Java keystore
keytool -importkeystore -deststorepass changeit -destkeypass changeit -destkeystore certs/localhost.jks -srckeystore certs/localhost.p12 -srcstoretype PKCS12 -srcstorepass changeit -alias localhost

echo "✅ SSL certificates generated successfully!"
echo ""
echo "📁 Generated files:"
echo "  - certs/localhost.key  (Private key)"
echo "  - certs/localhost.crt  (Certificate)"
echo "  - certs/localhost.p12  (PKCS12 format for backend)"
echo "  - certs/localhost.jks  (Java keystore for Spring Boot)"
echo ""
echo "🔧 Next steps:"
echo "  1. Trust the certificate in your browser/system"
echo "  2. Start the backend with HTTPS configuration"
echo "  3. Start the frontend with HTTPS configuration"
echo ""
echo "⚠️  Note: You may need to accept the self-signed certificate in your browser"
echo "    or add it to your system's trusted certificates for production-like testing."

# Make certificates readable
chmod 644 certs/localhost.crt certs/localhost.key certs/localhost.p12 certs/localhost.jks

echo ""
echo "🎙️ HTTPS is required for voice features to work properly in modern browsers!"
echo "   Voice recording requires a secure context (HTTPS) for getUserMedia() API."
