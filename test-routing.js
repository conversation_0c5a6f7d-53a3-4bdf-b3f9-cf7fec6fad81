#!/usr/bin/env node

/**
 * Simple routing test script to verify the bug tracking application routing
 * This script tests the main routing scenarios to ensure they work correctly
 */

const http = require('http');

const BASE_URL = 'http://localhost:3000';

// Test cases for SPA routing (all should return 200 with HTML)
const testCases = [
  {
    name: 'Root URL access',
    path: '/',
    description: 'Should serve the React app HTML (client-side routing will handle redirect)'
  },
  {
    name: 'Welcome page direct access',
    path: '/welcome',
    description: 'Should serve the React app HTML (client-side routing will show welcome page)'
  },
  {
    name: 'Invalid route',
    path: '/invalid-route-that-does-not-exist',
    description: 'Should serve the React app HTML (client-side routing will show 404 page)'
  },
  {
    name: 'Tenant selection page',
    path: '/tenant-selection',
    description: 'Should serve the React app HTML (client-side routing will show tenant selection)'
  },
  {
    name: 'Protected route without auth',
    path: '/app/dashboard',
    description: 'Should serve the React app HTML (client-side routing will handle auth redirect)'
  }
];

/**
 * Make HTTP request and check response
 */
function testRoute(testCase) {
  return new Promise((resolve) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: testCase.path,
      method: 'GET',
      headers: {
        'User-Agent': 'Routing-Test-Script'
      }
    };

    const req = http.request(options, (res) => {
      const isSuccess = res.statusCode >= 200 && res.statusCode < 300;
      const isHtml = res.headers['content-type'] && res.headers['content-type'].includes('text/html');

      let result = {
        name: testCase.name,
        path: testCase.path,
        statusCode: res.statusCode,
        isSuccess,
        isHtml,
        contentType: res.headers['content-type'] || 'unknown',
        passed: false,
        message: ''
      };

      // For SPA, all routes should return 200 with HTML content
      if (isSuccess && isHtml) {
        result.passed = true;
        result.message = `✅ Successfully served React app HTML (${res.statusCode})`;
      } else if (isSuccess && !isHtml) {
        result.passed = false;
        result.message = `❌ Returned ${res.statusCode} but not HTML content (${result.contentType})`;
      } else {
        result.passed = false;
        result.message = `❌ Unexpected response: ${res.statusCode}`;
      }

      resolve(result);
    });

    req.on('error', (err) => {
      resolve({
        name: testCase.name,
        path: testCase.path,
        passed: false,
        message: `❌ Request failed: ${err.message}`,
        error: err
      });
    });

    req.setTimeout(5000, () => {
      req.destroy();
      resolve({
        name: testCase.name,
        path: testCase.path,
        passed: false,
        message: `❌ Request timeout`,
        error: new Error('Timeout')
      });
    });

    req.end();
  });
}

/**
 * Run all routing tests
 */
async function runTests() {
  console.log('🚀 Starting Bug Tracking System Routing Tests\n');
  console.log(`Testing against: ${BASE_URL}\n`);

  const results = [];

  for (const testCase of testCases) {
    console.log(`Testing: ${testCase.name}`);
    console.log(`Path: ${testCase.path}`);
    console.log(`Description: ${testCase.description}`);

    const result = await testRoute(testCase);
    results.push(result);

    console.log(result.message);
    console.log('---');
  }

  // Summary
  const passed = results.filter(r => r.passed).length;
  const total = results.length;

  console.log('\n📊 Test Summary:');
  console.log(`Passed: ${passed}/${total}`);
  console.log(`Failed: ${total - passed}/${total}`);

  if (passed === total) {
    console.log('\n🎉 All routing tests passed! The routing fix is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the routing configuration.');

    // Show failed tests
    const failed = results.filter(r => !r.passed);
    if (failed.length > 0) {
      console.log('\nFailed tests:');
      failed.forEach(test => {
        console.log(`- ${test.name}: ${test.message}`);
      });
    }
  }

  return passed === total;
}

// Run tests if script is executed directly
if (require.main === module) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(err => {
    console.error('Test execution failed:', err);
    process.exit(1);
  });
}

module.exports = { runTests, testRoute };
