# API Configuration Guide

This document explains how the API configuration is centralized in the Bug Tracking System.

## Overview

The API configuration has been centralized to make it easier to change the API host and port in one place. The backend runs on port 8081, and the frontend is configured to connect to it.

## Environment Variables

The following environment variables are used:

- `VITE_API_HOST` - The hostname of the API server (default: `localhost`)
- `VITE_API_PORT` - The port of the API server (default: `8081`)

## Configuration Files

### Frontend Configuration

The frontend uses the following configuration files:

#### 1. `frontend/src/config/apiConfig.ts`

This file contains the centralized API configuration:

```typescript
// Base URL for API
export const API_PORT = import.meta.env.VITE_API_PORT || '8081';
export const API_HOST = import.meta.env.VITE_API_HOST || 'localhost';
export const API_BASE_URL = `http://${API_HOST}:${API_PORT}`;
```

#### 2. `frontend/vite.config.ts`

The Vite configuration uses environment variables for the development server proxy:

```typescript
import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  const env = loadEnv(mode, process.cwd(), '')
  
  // API configuration
  const API_PORT = env.VITE_API_PORT || '8081'
  const API_HOST = env.VITE_API_HOST || 'localhost'
  const DEV_PROXY_TARGET = `http://${API_HOST}:${API_PORT}`
  
  return {
    plugins: [react()],
    server: {
      port: 3000,
      proxy: {
        '/api': {
          target: DEV_PROXY_TARGET,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path.replace(/^\/api/, '/api')
        }
      }
    }
  }
})
```

#### 3. `frontend/src/config/dockerConfig.ts`

This file contains Docker-specific configuration:

```typescript
// Docker service names
export const DOCKER_BACKEND_SERVICE = 'backend';

// Docker proxy configuration for nginx
export const DOCKER_PROXY_PASS = `http://${DOCKER_BACKEND_SERVICE}:8081/api`;
```

#### 4. `frontend/scripts/generate-nginx-conf.js`

This script generates the nginx.conf file for Docker:

```javascript
const fs = require('fs');
const path = require('path');

// Define the backend service and port
const BACKEND_SERVICE = process.env.BACKEND_SERVICE || 'backend';
const BACKEND_PORT = process.env.BACKEND_PORT || '8081';
const DOCKER_PROXY_PASS = `http://${BACKEND_SERVICE}:${BACKEND_PORT}/api`;

const nginxTemplate = `server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass ${DOCKER_PROXY_PASS};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
`;

// Write the nginx.conf file
fs.writeFileSync(path.join(__dirname, '../nginx.conf'), nginxTemplate);
console.log('Generated nginx.conf with proxy_pass:', DOCKER_PROXY_PASS);
```

### Environment Files

#### 1. `frontend/.env.local`

This file contains local development environment variables:

```
# Local development environment variables
VITE_API_HOST=localhost
VITE_API_PORT=8081
```

#### 2. `.env`

This file contains default environment variables:

```
# API Configuration
API_HOST=localhost
API_PORT=8081

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=bugtracker
DB_USER=postgres
DB_PASSWORD=postgres

# Frontend Configuration
FRONTEND_PORT=3000
```

#### 3. `.env.docker`

This file contains Docker-specific environment variables:

```
# API Configuration for Docker
API_HOST=backend
API_PORT=8081

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=bugtracker
DB_USER=postgres
DB_PASSWORD=postgres

# Frontend Configuration
FRONTEND_PORT=3000
```

## Docker Configuration

The Docker configuration uses environment variables from `.env.docker`:

```yaml
version: '3.8'

services:
  postgres:
    # ...
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    # ...

  backend:
    # ...
    environment:
      SPRING_DATASOURCE_URL: jdbc:postgresql://${DB_HOST}:5432/${DB_NAME}
      SPRING_DATASOURCE_USERNAME: ${DB_USER}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD}
      SERVER_PORT: ${API_PORT}
    ports:
      - "${API_PORT}:${API_PORT}"
    # ...

  frontend:
    # ...
    environment:
      - VITE_API_HOST=${API_HOST}
      - VITE_API_PORT=${API_PORT}
    ports:
      - "${FRONTEND_PORT}:80"
    # ...
```

## Usage

### Local Development

1. Create a `.env.local` file in the `frontend` directory with the following content:
   ```
   VITE_API_HOST=localhost
   VITE_API_PORT=8081
   ```

2. Start the backend:
   ```
   cd backend
   ./mvnw spring-boot:run
   ```

3. Start the frontend:
   ```
   cd frontend
   npm run dev
   ```

### Docker

1. Start the Docker containers:
   ```
   ./docker-start.sh
   ```

## Changing the API Port

To change the API port:

1. Update the `.env` file:
   ```
   API_PORT=8081
   ```

2. Update the `.env.docker` file:
   ```
   API_PORT=8081
   ```

3. Update the `backend/src/main/resources/application.yml` file:
   ```yaml
   server:
     port: 8081
   ```

4. Update the `frontend/.env.local` file:
   ```
   VITE_API_PORT=8081
   ```

5. Restart the application
