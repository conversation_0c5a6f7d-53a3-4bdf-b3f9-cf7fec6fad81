# Multitenant Bug Tracking System

## Overview

This is a comprehensive multitenant bug tracking system that provides complete data isolation, security, and scalability for multiple organizations. Each tenant (organization) gets their own isolated environment with dedicated database schemas, user management, and customizable settings.

## 🏗️ Architecture

### Multitenant Strategy: Schema-per-Tenant
- **Complete Data Isolation**: Each tenant has a dedicated database schema
- **Security**: No possibility of cross-tenant data access
- **Scalability**: Easy to add new tenants without affecting existing ones
- **Performance**: Optimized connection pooling and caching per tenant

### Key Components
1. **Tenant Management**: Registration, configuration, and lifecycle management
2. **Dynamic Schema Switching**: Runtime tenant context resolution
3. **Tenant-Aware Authentication**: JWT tokens with tenant information
4. **Data Isolation**: Complete separation of tenant data
5. **Subscription Management**: Plans, limits, and billing support

## 🚀 Quick Start

### Prerequisites
- PostgreSQL 12+
- Java 21
- Node.js 18+
- Maven 3.8+

### 1. Start the System
```bash
# Make scripts executable
chmod +x start-multitenant.sh stop-multitenant.sh

# Start everything
./start-multitenant.sh
```

This will:
- Set up the database
- Build and start the backend (port 8081)
- Install dependencies and start the frontend (port 3000)
- Create a demo tenant for testing

### 2. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8081
- **API Documentation**: http://localhost:8081/swagger-ui.html

### 3. Test with Demo Tenant
- **Login URL**: http://localhost:3000/login?tenantId=demo
- **Username**: demo_admin
- **Password**: DemoPass123!

## 📋 Features

### Tenant Management
- ✅ Tenant registration with validation
- ✅ Subdomain-based access
- ✅ Subscription plans (Basic, Professional, Enterprise)
- ✅ User and storage limits
- ✅ Tenant status management (Active, Suspended, Inactive)
- ✅ Admin dashboard for tenant management

### Security & Authentication
- ✅ Tenant-aware JWT authentication
- ✅ Role-based access control per tenant
- ✅ Request-level tenant validation
- ✅ Secure tenant context management
- ✅ Cross-tenant data isolation

### Data Management
- ✅ Automatic schema creation for new tenants
- ✅ Flyway migrations for tenant schemas
- ✅ Connection pooling per tenant
- ✅ Performance optimizations

### Frontend Features
- ✅ Tenant registration form
- ✅ Tenant-aware login
- ✅ Tenant selection page
- ✅ Admin tenant management dashboard
- ✅ Route guards with tenant validation

## 🔧 Configuration

### Backend Configuration (`application.yml`)
```yaml
# Multitenant Configuration
multitenant:
  master:
    schema: public
  tenant:
    schema-prefix: tenant_
    default-max-users: 100
    default-max-storage-mb: 1000

# Database Configuration
spring:
  datasource:
    url: ****************************************
    username: postgres
    password: password
  jpa:
    properties:
      hibernate:
        multiTenancy: SCHEMA
        multi_tenant_connection_provider: com.bugtracker.config.MultitenantConnectionProvider
        tenant_identifier_resolver: com.bugtracker.config.TenantIdentifierResolver
```

### Environment Variables
```bash
# Database
DB_URL=****************************************
DB_USERNAME=postgres
DB_PASSWORD=password

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRATION=86400

# Application
JAVA_HOME=/usr/local/opt/openjdk@21/libexec/openjdk.jdk/Contents/Home
```

## 🌐 API Endpoints

### Tenant Management
```
POST   /api/tenants/register           # Register new tenant
GET    /api/tenants                    # List all tenants (admin)
GET    /api/tenants/{id}               # Get tenant by ID
GET    /api/tenants/by-tenant-id/{id}  # Get tenant by tenant ID
GET    /api/tenants/by-subdomain/{sub} # Get tenant by subdomain
PUT    /api/tenants/{id}               # Update tenant
POST   /api/tenants/{id}/suspend       # Suspend tenant
POST   /api/tenants/{id}/activate      # Activate tenant
DELETE /api/tenants/{id}               # Delete tenant
```

### Tenant-Aware Authentication
```
POST   /api/auth/signin                # Tenant-scoped login
POST   /api/auth/signup                # Tenant-scoped registration
GET    /api/auth/me                    # Current user info
POST   /api/auth/logout                # Logout
GET    /api/auth/tenant-status         # Tenant status check
```

### Application APIs (Tenant-Aware)
All existing APIs now work within tenant context:
```
GET    /api/issues                     # Get issues for current tenant
POST   /api/issues                     # Create issue in current tenant
GET    /api/users                      # Get users for current tenant
# ... all other APIs
```

## 🔐 Tenant Resolution

The system supports multiple ways to identify tenants:

### 1. Subdomain-based (Recommended)
```
https://acme.bugtracker.com/dashboard
```

### 2. Header-based
```bash
curl -H "X-Tenant-ID: acme" http://localhost:8081/api/issues
```

### 3. URL Parameter
```
http://localhost:3000/login?tenantId=acme
```

## 🧪 Testing

### Register a New Tenant
```bash
curl -X POST http://localhost:8081/api/tenants/register \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "testcorp",
    "name": "Test Corporation",
    "subdomain": "testcorp",
    "adminEmail": "<EMAIL>",
    "adminFirstName": "Test",
    "adminLastName": "Admin",
    "adminPassword": "TestPass123!",
    "description": "Test tenant for development"
  }'
```

### Login to Tenant
```bash
curl -X POST http://localhost:8081/api/auth/signin \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: testcorp" \
  -d '{
    "username": "testcorp_admin",
    "password": "TestPass123!"
  }'
```

### Test Data Isolation
```bash
# Create issue in tenant 1
curl -X POST http://localhost:8081/api/issues \
  -H "Authorization: Bearer $TOKEN1" \
  -H "X-Tenant-ID: tenant1" \
  -H "Content-Type: application/json" \
  -d '{"title": "Tenant 1 Issue", "type": "Bug", "priority": "High"}'

# Verify tenant 2 cannot see tenant 1's issues
curl -X GET http://localhost:8081/api/issues \
  -H "Authorization: Bearer $TOKEN2" \
  -H "X-Tenant-ID: tenant2"
```

## 📊 Database Schema

### Master Schema (public)
```sql
tenants                 -- Core tenant information
tenant_usage           -- Usage metrics per tenant
tenant_settings        -- Tenant-specific configurations
tenant_audit_log       -- Audit trail for tenant operations
```

### Tenant Schema (tenant_<tenant_id>)
```sql
users, roles, user_roles
issues, comments, attachments
notifications, labels, checklist_items
lookup_values, issue_counters
-- Complete application schema per tenant
```

## 🔍 Monitoring

### Check Tenant Schemas
```sql
-- List all tenant schemas
SELECT schema_name FROM information_schema.schemata 
WHERE schema_name LIKE 'tenant_%';

-- Check tenant data
SET search_path TO tenant_acme;
SELECT COUNT(*) FROM issues;
```

### Application Logs
```bash
# Backend logs
tail -f logs/backend.log

# Frontend logs  
tail -f logs/frontend.log
```

## 🛠️ Development

### Adding New Features
1. **Backend**: Ensure all new entities/services are tenant-aware
2. **Frontend**: Use tenant context in all API calls
3. **Database**: Add migrations to both master and tenant schemas as needed

### Best Practices
- Always validate tenant context in controllers
- Use tenant-aware repositories and services
- Include tenant information in audit logs
- Test cross-tenant data isolation
- Monitor connection pool usage per tenant

## 🚫 Stopping the System
```bash
./stop-multitenant.sh
```

## 📚 Additional Resources

- [Multitenant Implementation Summary](MULTITENANT_IMPLEMENTATION_SUMMARY.md)
- [Testing Guide](MULTITENANT_TESTING_GUIDE.md)
- [API Documentation](http://localhost:8081/swagger-ui.html)

## 🤝 Contributing

1. Ensure all new features maintain tenant isolation
2. Add appropriate tests for multitenant scenarios
3. Update documentation for any new tenant-related features
4. Follow the existing patterns for tenant context management

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Note**: This is a production-ready multitenant system with complete data isolation, security, and scalability features. Each tenant operates in a completely isolated environment with no possibility of cross-tenant data access.
