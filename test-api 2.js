const axios = require('axios');

// Create an axios instance with CORS headers
const api = axios.create({
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization'
  }
});

// Test API endpoints
async function testApi() {
  console.log('Testing API connection to backend...');

  try {
    // Test health endpoint
    console.log('\nTesting health endpoint:');
    try {
      const healthResponse = await api.get('http://localhost:8081/actuator/health');
      console.log('Health endpoint response:', healthResponse.status, healthResponse.data);
    } catch (error) {
      console.log('Health endpoint error:', error.response?.status, error.response?.data);
    }

    // Test users endpoint
    console.log('\nTesting users endpoint:');
    try {
      const usersResponse = await api.get('http://localhost:8081/api/users');
      console.log('Users endpoint response:', usersResponse.status, usersResponse.data);
    } catch (error) {
      console.log('Users endpoint error:', error.response?.status, error.response?.data);
    }

    // Test signup endpoint
    console.log('\nTesting signup endpoint:');
    try {
      const signupResponse = await api.post('http://localhost:8081/api/auth/signup', {
        username: 'newuser3',
        email: '<EMAIL>',
        password: 'password',
        firstName: 'New',
        lastName: 'User',
        roles: ['reporter']
      });
      console.log('Signup endpoint response:', signupResponse.status, signupResponse.data);
    } catch (error) {
      console.log('Signup endpoint error:', error.response?.status, error.response?.data);
    }

    // Test login endpoint with different credentials
    console.log('\nTesting login endpoint with different credentials:');

    const testCredentials = [
      { username: 'testuser', password: 'password' },
      { username: 'testuser2', password: 'password' },
      { username: 'admin', password: 'password' },
      { username: 'newuser', password: 'password' },
      { username: 'newuser2', password: 'password' },
      { username: 'newuser3', password: 'password' },
      { username: 'testadmin', password: 'password' }
    ];

    for (const creds of testCredentials) {
      console.log(`\nTrying login with username: ${creds.username}`);
      try {
        const loginResponse = await api.post('http://localhost:8081/api/auth/signin', creds);
        console.log('Login endpoint response:', loginResponse.status, loginResponse.data);

        // If login is successful, use the token to access protected endpoints
        if (loginResponse.data && loginResponse.data.token) {
          const token = loginResponse.data.token;
          console.log('\nTesting users endpoint with token:');
          try {
            const usersResponse = await api.get('http://localhost:8081/api/users', {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });
            console.log('Users endpoint response with token:', usersResponse.status, usersResponse.data);
          } catch (error) {
            console.log('Users endpoint error with token:', error.response?.status, error.response?.data);
          }
        }
      } catch (error) {
        console.log('Login endpoint error:', error.response?.status, error.response?.data);
      }
    }

  } catch (error) {
    console.error('General error:', error.message);
  }
}

// Run the test
testApi();
