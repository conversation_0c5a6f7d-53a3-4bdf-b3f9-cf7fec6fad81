package com.bugtracker.repository;

import com.bugtracker.model.PerformanceMetric;
import com.bugtracker.model.User;
import com.bugtracker.model.Issue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for Performance Metric entities
 */
@Repository
public interface PerformanceMetricRepository extends JpaRepository<PerformanceMetric, Long> {

    /**
     * Find performance metrics by type
     */
    List<PerformanceMetric> findByMetricTypeOrderByRecordedAtDesc(String metricType);

    /**
     * Find performance metrics by type within date range
     */
    @Query("SELECT pm FROM PerformanceMetric pm WHERE pm.metricType = :metricType AND pm.recordedAt BETWEEN :startDate AND :endDate ORDER BY pm.recordedAt DESC")
    List<PerformanceMetric> findByMetricTypeAndRecordedAtBetween(@Param("metricType") String metricType, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find performance metrics by user
     */
    List<PerformanceMetric> findByUserOrderByRecordedAtDesc(User user);

    /**
     * Find performance metrics by issue
     */
    List<PerformanceMetric> findByIssueOrderByRecordedAtDesc(Issue issue);

    /**
     * Find performance metrics within date range
     */
    @Query("SELECT pm FROM PerformanceMetric pm WHERE pm.recordedAt BETWEEN :startDate AND :endDate ORDER BY pm.recordedAt DESC")
    List<PerformanceMetric> findByRecordedAtBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Calculate average metric value by type
     */
    @Query("SELECT AVG(pm.metricValue) FROM PerformanceMetric pm WHERE pm.metricType = :metricType")
    BigDecimal calculateAverageByMetricType(@Param("metricType") String metricType);

    /**
     * Calculate average metric value by type within date range
     */
    @Query("SELECT AVG(pm.metricValue) FROM PerformanceMetric pm WHERE pm.metricType = :metricType AND pm.recordedAt BETWEEN :startDate AND :endDate")
    BigDecimal calculateAverageByMetricTypeAndDateRange(@Param("metricType") String metricType, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find maximum metric value by type
     */
    @Query("SELECT MAX(pm.metricValue) FROM PerformanceMetric pm WHERE pm.metricType = :metricType")
    BigDecimal findMaxByMetricType(@Param("metricType") String metricType);

    /**
     * Find minimum metric value by type
     */
    @Query("SELECT MIN(pm.metricValue) FROM PerformanceMetric pm WHERE pm.metricType = :metricType")
    BigDecimal findMinByMetricType(@Param("metricType") String metricType);

    /**
     * Find metrics with values above threshold
     */
    @Query("SELECT pm FROM PerformanceMetric pm WHERE pm.metricType = :metricType AND pm.metricValue > :threshold ORDER BY pm.metricValue DESC")
    List<PerformanceMetric> findByMetricTypeAndValueGreaterThan(@Param("metricType") String metricType, @Param("threshold") BigDecimal threshold);

    /**
     * Find metrics with values below threshold
     */
    @Query("SELECT pm FROM PerformanceMetric pm WHERE pm.metricType = :metricType AND pm.metricValue < :threshold ORDER BY pm.metricValue ASC")
    List<PerformanceMetric> findByMetricTypeAndValueLessThan(@Param("metricType") String metricType, @Param("threshold") BigDecimal threshold);

    /**
     * Count metrics by type
     */
    long countByMetricType(String metricType);

    /**
     * Find recent metrics (last 24 hours)
     */
    @Query("SELECT pm FROM PerformanceMetric pm WHERE pm.recordedAt > :since ORDER BY pm.recordedAt DESC")
    List<PerformanceMetric> findRecentMetrics(@Param("since") LocalDateTime since);

    /**
     * Find performance trends by type (hourly aggregation)
     */
    @Query("SELECT DATE_TRUNC('hour', pm.recordedAt) as hour, AVG(pm.metricValue) as avgValue, COUNT(pm) as count " +
           "FROM PerformanceMetric pm WHERE pm.metricType = :metricType AND pm.recordedAt BETWEEN :startDate AND :endDate " +
           "GROUP BY DATE_TRUNC('hour', pm.recordedAt) ORDER BY hour")
    List<Object[]> findHourlyTrendsByMetricType(@Param("metricType") String metricType, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find performance trends by type (daily aggregation)
     */
    @Query("SELECT DATE_TRUNC('day', pm.recordedAt) as day, AVG(pm.metricValue) as avgValue, COUNT(pm) as count " +
           "FROM PerformanceMetric pm WHERE pm.metricType = :metricType AND pm.recordedAt BETWEEN :startDate AND :endDate " +
           "GROUP BY DATE_TRUNC('day', pm.recordedAt) ORDER BY day")
    List<Object[]> findDailyTrendsByMetricType(@Param("metricType") String metricType, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find top performing users by metric type
     */
    @Query("SELECT pm.user, AVG(pm.metricValue) as avgValue FROM PerformanceMetric pm WHERE pm.metricType = :metricType AND pm.user IS NOT NULL " +
           "GROUP BY pm.user ORDER BY avgValue ASC")
    List<Object[]> findTopPerformingUsersByMetricType(@Param("metricType") String metricType);

    /**
     * Find performance summary by metric type
     */
    @Query("SELECT pm.metricType, COUNT(pm), AVG(pm.metricValue), MIN(pm.metricValue), MAX(pm.metricValue) " +
           "FROM PerformanceMetric pm WHERE pm.recordedAt BETWEEN :startDate AND :endDate " +
           "GROUP BY pm.metricType ORDER BY pm.metricType")
    List<Object[]> findPerformanceSummaryByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find metrics that indicate poor performance
     */
    @Query("SELECT pm FROM PerformanceMetric pm WHERE " +
           "(pm.metricType IN ('API_RESPONSE_TIME', 'DB_QUERY_TIME', 'AI_PROCESSING_TIME') AND pm.metricValue > 1000) OR " +
           "(pm.metricType = 'PAGE_LOAD_TIME' AND pm.metricValue > 3000) OR " +
           "(pm.metricType = 'ERROR_RATE' AND pm.metricValue > 5) OR " +
           "(pm.metricType IN ('CPU_USAGE', 'MEMORY_USAGE') AND pm.metricValue > 80) " +
           "ORDER BY pm.recordedAt DESC")
    List<PerformanceMetric> findPoorPerformanceMetrics();

    /**
     * Delete old metrics (older than specified date)
     */
    void deleteByRecordedAtBefore(LocalDateTime cutoffDate);

    /**
     * Find distinct metric types
     */
    @Query("SELECT DISTINCT pm.metricType FROM PerformanceMetric pm ORDER BY pm.metricType")
    List<String> findDistinctMetricTypes();

    /**
     * Find metrics by metric name pattern
     */
    @Query("SELECT pm FROM PerformanceMetric pm WHERE pm.metricName LIKE %:pattern% ORDER BY pm.recordedAt DESC")
    List<PerformanceMetric> findByMetricNameContaining(@Param("pattern") String pattern);
}
