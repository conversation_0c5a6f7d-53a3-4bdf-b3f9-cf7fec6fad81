package com.bugtracker.repository;

import com.bugtracker.model.SlaTracking;
import com.bugtracker.model.Issue;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for SLA Tracking entities
 */
@Repository
public interface SlaTrackingRepository extends JpaRepository<SlaTracking, Long> {

    /**
     * Find SLA tracking by issue
     */
    Optional<SlaTracking> findByIssue(Issue issue);

    /**
     * Find SLA tracking by issue ID
     */
    Optional<SlaTracking> findByIssueId(Long issueId);

    /**
     * Find all active SLA trackings
     */
    List<SlaTracking> findBySlaStatusOrderByCreatedAtDesc(String slaStatus);

    /**
     * Find SLA trackings with breached response time
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.responseDate IS NULL AND st.responseDueDate < :currentTime")
    List<SlaTracking> findResponseBreached(@Param("currentTime") LocalDateTime currentTime);

    /**
     * Find SLA trackings with breached resolution time
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.resolutionDate IS NULL AND st.resolutionDueDate < :currentTime")
    List<SlaTracking> findResolutionBreached(@Param("currentTime") LocalDateTime currentTime);

    /**
     * Find SLA trackings that need escalation
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.escalationTriggered = false AND st.escalationDueDate < :currentTime")
    List<SlaTracking> findEscalationDue(@Param("currentTime") LocalDateTime currentTime);

    /**
     * Find SLA trackings by status
     */
    List<SlaTracking> findBySlaStatus(String slaStatus);

    /**
     * Find SLA trackings with response due within specified hours
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.responseDate IS NULL AND st.responseDueDate BETWEEN :now AND :futureTime")
    List<SlaTracking> findResponseDueWithin(@Param("now") LocalDateTime now, @Param("futureTime") LocalDateTime futureTime);

    /**
     * Find SLA trackings with resolution due within specified hours
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.resolutionDate IS NULL AND st.resolutionDueDate BETWEEN :now AND :futureTime")
    List<SlaTracking> findResolutionDueWithin(@Param("now") LocalDateTime now, @Param("futureTime") LocalDateTime futureTime);

    /**
     * Count SLA breaches by type
     */
    @Query("SELECT COUNT(st) FROM SlaTracking st WHERE st.responseDate IS NULL AND st.responseDueDate < :currentTime")
    long countResponseBreaches(@Param("currentTime") LocalDateTime currentTime);

    @Query("SELECT COUNT(st) FROM SlaTracking st WHERE st.resolutionDate IS NULL AND st.resolutionDueDate < :currentTime")
    long countResolutionBreaches(@Param("currentTime") LocalDateTime currentTime);

    /**
     * Find SLA trackings created within date range
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.createdAt BETWEEN :startDate AND :endDate ORDER BY st.createdAt DESC")
    List<SlaTracking> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find completed SLA trackings (with resolution date)
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.resolutionDate IS NOT NULL ORDER BY st.resolutionDate DESC")
    List<SlaTracking> findCompleted();

    /**
     * Calculate average response time for completed SLAs (database-agnostic)
     * Note: This method will be implemented in the service layer to avoid database-specific functions
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.responseDate IS NOT NULL")
    List<SlaTracking> findCompletedResponseSlas();

    /**
     * Calculate average resolution time for completed SLAs (database-agnostic)
     * Note: This method will be implemented in the service layer to avoid database-specific functions
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.resolutionDate IS NOT NULL")
    List<SlaTracking> findCompletedResolutionSlas();

    /**
     * Find SLA trackings by issue severity
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.issue.severity = :severity ORDER BY st.createdAt DESC")
    List<SlaTracking> findByIssueSeverity(@Param("severity") String severity);

    /**
     * Find SLA trackings by issue priority
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.issue.priority = :priority ORDER BY st.createdAt DESC")
    List<SlaTracking> findByIssuePriority(@Param("priority") String priority);

    /**
     * Find SLA trackings by issue environment
     */
    @Query("SELECT st FROM SlaTracking st WHERE st.issue.environment = :environment ORDER BY st.createdAt DESC")
    List<SlaTracking> findByIssueEnvironment(@Param("environment") String environment);

    long countBySlaStatus(String active);
}
