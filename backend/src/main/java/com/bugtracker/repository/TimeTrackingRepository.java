package com.bugtracker.repository;

import com.bugtracker.model.TimeTracking;
import com.bugtracker.model.Issue;
import com.bugtracker.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Time Tracking entities
 */
@Repository
public interface TimeTrackingRepository extends JpaRepository<TimeTracking, Long> {

    /**
     * Find time tracking entries by issue
     */
    List<TimeTracking> findByIssueOrderByStartTimeDesc(Issue issue);

    /**
     * Find time tracking entries by issue ID
     */
    List<TimeTracking> findByIssueIdOrderByStartTimeDesc(Long issueId);

    /**
     * Find time tracking entries by user
     */
    List<TimeTracking> findByUserOrderByStartTimeDesc(User user);

    /**
     * Find time tracking entries by user ID
     */
    List<TimeTracking> findByUserIdOrderByStartTimeDesc(Long userId);

    /**
     * Find active time tracking entries (no end time)
     */
    List<TimeTracking> findByEndTimeIsNullOrderByStartTimeDesc();

    /**
     * Find active time tracking entries by user
     */
    List<TimeTracking> findByUserAndEndTimeIsNullOrderByStartTimeDesc(User user);

    /**
     * Find active time tracking entry by user ID
     */
    Optional<TimeTracking> findByUserIdAndEndTimeIsNull(Long userId);

    /**
     * Find time tracking entries by activity type
     */
    List<TimeTracking> findByActivityTypeOrderByStartTimeDesc(String activityType);

    /**
     * Find time tracking entries within date range
     */
    @Query("SELECT tt FROM TimeTracking tt WHERE tt.startTime BETWEEN :startDate AND :endDate ORDER BY tt.startTime DESC")
    List<TimeTracking> findByStartTimeBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find time tracking entries by user within date range
     */
    @Query("SELECT tt FROM TimeTracking tt WHERE tt.user = :user AND tt.startTime BETWEEN :startDate AND :endDate ORDER BY tt.startTime DESC")
    List<TimeTracking> findByUserAndStartTimeBetween(@Param("user") User user, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find billable time tracking entries
     */
    List<TimeTracking> findByBillableTrueOrderByStartTimeDesc();

    /**
     * Find billable time tracking entries by user
     */
    List<TimeTracking> findByUserAndBillableTrueOrderByStartTimeDesc(User user);

    /**
     * Calculate total time spent on issue
     */
    @Query("SELECT COALESCE(SUM(tt.durationMinutes), 0) FROM TimeTracking tt WHERE tt.issue = :issue AND tt.endTime IS NOT NULL")
    Long calculateTotalTimeMinutesByIssue(@Param("issue") Issue issue);

    /**
     * Calculate total time spent by user
     */
    @Query("SELECT COALESCE(SUM(tt.durationMinutes), 0) FROM TimeTracking tt WHERE tt.user = :user AND tt.endTime IS NOT NULL")
    Long calculateTotalTimeMinutesByUser(@Param("user") User user);

    /**
     * Calculate total billable time by user
     */
    @Query("SELECT COALESCE(SUM(tt.durationMinutes), 0) FROM TimeTracking tt WHERE tt.user = :user AND tt.billable = true AND tt.endTime IS NOT NULL")
    Long calculateBillableTimeMinutesByUser(@Param("user") User user);

    /**
     * Calculate total time by activity type
     */
    @Query("SELECT COALESCE(SUM(tt.durationMinutes), 0) FROM TimeTracking tt WHERE tt.activityType = :activityType AND tt.endTime IS NOT NULL")
    Long calculateTotalTimeMinutesByActivityType(@Param("activityType") String activityType);

    /**
     * Find time tracking entries by user and activity type
     */
    List<TimeTracking> findByUserAndActivityTypeOrderByStartTimeDesc(User user, String activityType);

    /**
     * Find time tracking entries by issue and activity type
     */
    List<TimeTracking> findByIssueAndActivityTypeOrderByStartTimeDesc(Issue issue, String activityType);

    /**
     * Calculate average time per issue by activity type
     */
    @Query("SELECT AVG(tt.durationMinutes) FROM TimeTracking tt WHERE tt.activityType = :activityType AND tt.endTime IS NOT NULL")
    Double calculateAverageTimeMinutesByActivityType(@Param("activityType") String activityType);

    /**
     * Find top users by total time spent
     */
    @Query("SELECT tt.user, SUM(tt.durationMinutes) as totalTime FROM TimeTracking tt WHERE tt.endTime IS NOT NULL GROUP BY tt.user ORDER BY totalTime DESC")
    List<Object[]> findTopUsersByTotalTime();

    /**
     * Find time tracking statistics by date range
     */
    @Query("SELECT tt.activityType, COUNT(tt), SUM(tt.durationMinutes), AVG(tt.durationMinutes) " +
           "FROM TimeTracking tt WHERE tt.startTime BETWEEN :startDate AND :endDate AND tt.endTime IS NOT NULL " +
           "GROUP BY tt.activityType ORDER BY SUM(tt.durationMinutes) DESC")
    List<Object[]> findTimeStatsByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find longest running active time tracking entries
     */
    @Query("SELECT tt FROM TimeTracking tt WHERE tt.endTime IS NULL ORDER BY tt.startTime ASC")
    List<TimeTracking> findLongestRunningActiveTracks();

    /**
     * Count active time tracking entries by user
     */
    long countByUserAndEndTimeIsNull(User user);

    /**
     * Find time tracking entries that started today (database-agnostic)
     */
    @Query("SELECT tt FROM TimeTracking tt WHERE tt.startTime >= :startOfDay AND tt.startTime < :endOfDay ORDER BY tt.startTime DESC")
    List<TimeTracking> findTodaysTimeTracking(@Param("startOfDay") LocalDateTime startOfDay, @Param("endOfDay") LocalDateTime endOfDay);

    /**
     * Find time tracking entries by user for today (database-agnostic)
     */
    @Query("SELECT tt FROM TimeTracking tt WHERE tt.user = :user AND tt.startTime >= :startOfDay AND tt.startTime < :endOfDay ORDER BY tt.startTime DESC")
    List<TimeTracking> findTodaysTimeTrackingByUser(@Param("user") User user, @Param("startOfDay") LocalDateTime startOfDay, @Param("endOfDay") LocalDateTime endOfDay);
}
