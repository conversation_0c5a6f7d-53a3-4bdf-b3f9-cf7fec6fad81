package com.bugtracker.exception;

/**
 * Exception thrown when there's a conflict in generating unique issue identifiers
 * This exception is specifically for handling duplicate identifier constraint violations
 */
public class IssueIdentifierConflictException extends RuntimeException {
    
    private final String issueType;
    private final String attemptedIdentifier;
    private final int retryAttempts;
    
    public IssueIdentifierConflictException(String message) {
        super(message);
        this.issueType = null;
        this.attemptedIdentifier = null;
        this.retryAttempts = 0;
    }
    
    public IssueIdentifierConflictException(String message, Throwable cause) {
        super(message, cause);
        this.issueType = null;
        this.attemptedIdentifier = null;
        this.retryAttempts = 0;
    }
    
    public IssueIdentifierConflictException(String message, String issueType, String attemptedIdentifier, int retryAttempts) {
        super(message);
        this.issueType = issueType;
        this.attemptedIdentifier = attemptedIdentifier;
        this.retryAttempts = retryAttempts;
    }
    
    public IssueIdentifierConflictException(String message, String issueType, String attemptedIdentifier, int retryAttempts, Throwable cause) {
        super(message, cause);
        this.issueType = issueType;
        this.attemptedIdentifier = attemptedIdentifier;
        this.retryAttempts = retryAttempts;
    }
    
    public String getIssueType() {
        return issueType;
    }
    
    public String getAttemptedIdentifier() {
        return attemptedIdentifier;
    }
    
    public int getRetryAttempts() {
        return retryAttempts;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(": ").append(getMessage());
        
        if (issueType != null) {
            sb.append(" [Issue Type: ").append(issueType);
        }
        
        if (attemptedIdentifier != null) {
            sb.append(", Attempted Identifier: ").append(attemptedIdentifier);
        }
        
        if (retryAttempts > 0) {
            sb.append(", Retry Attempts: ").append(retryAttempts);
        }
        
        if (issueType != null || attemptedIdentifier != null || retryAttempts > 0) {
            sb.append("]");
        }
        
        return sb.toString();
    }
}
