package com.bugtracker.service.impl;

import com.bugtracker.model.TimeTracking;
import com.bugtracker.model.Issue;
import com.bugtracker.model.User;
import com.bugtracker.repository.TimeTrackingRepository;
import com.bugtracker.repository.IssueRepository;
import com.bugtracker.repository.UserRepository;
import com.bugtracker.service.TimeTrackingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for Time Tracking management
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TimeTrackingServiceImpl implements TimeTrackingService {

    private final TimeTrackingRepository timeTrackingRepository;
    private final IssueRepository issueRepository;
    private final UserRepository userRepository;

    @Override
    public TimeTracking startTimeTracking(Long issueId, Long userId, String activityType, String description) {
        log.debug("Starting time tracking for issue: {} by user: {}", issueId, userId);
        
        Issue issue = issueRepository.findById(issueId)
                .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issueId));
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        // Stop any active time tracking for this user
        stopActiveTimeTracking(userId);
        
        TimeTracking timeTracking = TimeTracking.builder()
                .issue(issue)
                .user(user)
                .activityType(activityType)
                .description(description)
                .startTime(LocalDateTime.now())
                .billable(true)
                .build();
        
        return timeTrackingRepository.save(timeTracking);
    }

    @Override
    public TimeTracking stopTimeTracking(Long timeTrackingId) {
        log.debug("Stopping time tracking: {}", timeTrackingId);
        
        TimeTracking timeTracking = timeTrackingRepository.findById(timeTrackingId)
                .orElseThrow(() -> new RuntimeException("Time tracking not found with id: " + timeTrackingId));
        
        if (timeTracking.getEndTime() != null) {
            throw new RuntimeException("Time tracking is already stopped");
        }
        
        timeTracking.stopTracking();
        return timeTrackingRepository.save(timeTracking);
    }

    @Override
    public Optional<TimeTracking> stopActiveTimeTracking(Long userId) {
        log.debug("Stopping active time tracking for user: {}", userId);
        
        Optional<TimeTracking> activeTracking = timeTrackingRepository.findByUserIdAndEndTimeIsNull(userId);
        if (activeTracking.isPresent()) {
            TimeTracking timeTracking = activeTracking.get();
            timeTracking.stopTracking();
            return Optional.of(timeTrackingRepository.save(timeTracking));
        }
        
        return Optional.empty();
    }

    @Override
    public TimeTracking createManualTimeEntry(Long issueId, Long userId, String activityType, 
                                            LocalDateTime startTime, LocalDateTime endTime, String description, Boolean billable) {
        log.debug("Creating manual time entry for issue: {} by user: {}", issueId, userId);
        
        Issue issue = issueRepository.findById(issueId)
                .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issueId));
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        TimeTracking timeTracking = TimeTracking.builder()
                .issue(issue)
                .user(user)
                .activityType(activityType)
                .description(description)
                .startTime(startTime)
                .endTime(endTime)
                .billable(billable != null ? billable : true)
                .build();
        
        timeTracking.calculateDuration();
        return timeTrackingRepository.save(timeTracking);
    }

    @Override
    public TimeTracking updateTimeTracking(TimeTracking timeTracking) {
        log.debug("Updating time tracking: {}", timeTracking.getId());
        
        if (!timeTrackingRepository.existsById(timeTracking.getId())) {
            throw new RuntimeException("Time tracking not found with id: " + timeTracking.getId());
        }
        
        timeTracking.calculateDuration();
        return timeTrackingRepository.save(timeTracking);
    }

    @Override
    public void deleteTimeTracking(Long id) {
        log.debug("Deleting time tracking: {}", id);
        
        if (!timeTrackingRepository.existsById(id)) {
            throw new RuntimeException("Time tracking not found with id: " + id);
        }
        
        timeTrackingRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TimeTracking> getTimeTrackingById(Long id) {
        return timeTrackingRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TimeTracking> getTimeTrackingByIssue(Long issueId) {
        return timeTrackingRepository.findByIssueIdOrderByStartTimeDesc(issueId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TimeTracking> getTimeTrackingByUser(Long userId) {
        return timeTrackingRepository.findByUserIdOrderByStartTimeDesc(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TimeTracking> getActiveTimeTrackingByUser(Long userId) {
        return timeTrackingRepository.findByUserIdAndEndTimeIsNull(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TimeTracking> getTimeTrackingByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return timeTrackingRepository.findByStartTimeBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TimeTracking> getTimeTrackingByUserAndDateRange(Long userId, LocalDateTime startDate, LocalDateTime endDate) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        return timeTrackingRepository.findByUserAndStartTimeBetween(user, startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TimeTracking> getTodaysTimeTrackingByUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        // Calculate start and end of today (database-agnostic)
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.plusDays(1).atStartOfDay();
        
        return timeTrackingRepository.findTodaysTimeTrackingByUser(user, startOfDay, endOfDay);
    }

    @Override
    @Transactional(readOnly = true)
    public Double getTotalTimeSpentOnIssue(Long issueId) {
        Issue issue = issueRepository.findById(issueId)
                .orElseThrow(() -> new RuntimeException("Issue not found with id: " + issueId));
        
        Long totalMinutes = timeTrackingRepository.calculateTotalTimeMinutesByIssue(issue);
        return totalMinutes / 60.0; // Convert to hours
    }

    @Override
    @Transactional(readOnly = true)
    public Double getTotalTimeSpentByUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        Long totalMinutes = timeTrackingRepository.calculateTotalTimeMinutesByUser(user);
        return totalMinutes / 60.0; // Convert to hours
    }

    @Override
    @Transactional(readOnly = true)
    public Double getBillableTimeByUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        Long billableMinutes = timeTrackingRepository.calculateBillableTimeMinutesByUser(user);
        return billableMinutes / 60.0; // Convert to hours
    }

    @Override
    @Transactional(readOnly = true)
    public TimeTrackingStatistics getTimeTrackingStatistics() {
        // Implementation will be added in next part
        return new TimeTrackingStatistics();
    }

    @Override
    @Transactional(readOnly = true)
    public TimeTrackingStatistics getTimeTrackingStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        // Implementation will be added in next part
        return new TimeTrackingStatistics();
    }

    @Override
    @Transactional(readOnly = true)
    public TimeTrackingStatistics getTimeTrackingStatisticsByUser(Long userId) {
        // Implementation will be added in next part
        return new TimeTrackingStatistics();
    }

    @Override
    @Transactional(readOnly = true)
    public List<TimeTracking> getTimeTrackingByActivityType(String activityType) {
        return timeTrackingRepository.findByActivityTypeOrderByStartTimeDesc(activityType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserTimeStatistics> getTopUsersByTimeSpent() {
        // Implementation will be added in next part
        return List.of();
    }

    @Override
    @Transactional(readOnly = true)
    public List<ActivityTimeStatistics> getTimeDistributionByActivityType() {
        // Implementation will be added in next part
        return List.of();
    }
}
