package com.bugtracker.service.impl;

import com.bugtracker.model.Issue;
import com.bugtracker.model.SlaConfiguration;
import com.bugtracker.model.SlaTracking;
import com.bugtracker.repository.SlaConfigurationRepository;
import com.bugtracker.repository.SlaTrackingRepository;
import com.bugtracker.service.SlaService;
import com.bugtracker.service.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of SLA Service
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SlaServiceImpl implements SlaService {

    private final SlaConfigurationRepository slaConfigurationRepository;
    private final SlaTrackingRepository slaTrackingRepository;
    private final NotificationService notificationService;

    @Override
    public SlaConfiguration createSlaConfiguration(SlaConfiguration slaConfiguration) {
        log.debug("Creating SLA configuration: {}", slaConfiguration);
        return slaConfigurationRepository.save(slaConfiguration);
    }

    @Override
    public SlaConfiguration updateSlaConfiguration(SlaConfiguration slaConfiguration) {
        log.debug("Updating SLA configuration: {}", slaConfiguration);
        return slaConfigurationRepository.save(slaConfiguration);
    }

    @Override
    public void deleteSlaConfiguration(Long id) {
        log.debug("Deleting SLA configuration with ID: {}", id);
        slaConfigurationRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SlaConfiguration> getSlaConfigurationById(Long id) {
        return slaConfigurationRepository.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SlaConfiguration> getAllActiveSlaConfigurations() {
        return slaConfigurationRepository.findByIsActiveTrueOrderByIssueTypeAscSeverityAscPriorityAsc();
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SlaConfiguration> findSlaConfigurationForIssue(Issue issue) {
        return slaConfigurationRepository.findByIssueTypeAndSeverityAndPriorityAndEnvironmentAndIsActiveTrue(
                issue.getType(), issue.getSeverity(), issue.getPriority(), issue.getEnvironment());
    }

    @Override
    public SlaTracking createSlaTracking(Issue issue) {
        log.debug("Creating SLA tracking for issue: {}", issue.getIdentifier());
        
        Optional<SlaConfiguration> slaConfigOpt = findSlaConfigurationForIssue(issue);
        if (slaConfigOpt.isEmpty()) {
            log.warn("No SLA configuration found for issue: {}", issue.getIdentifier());
            return null;
        }

        SlaConfiguration slaConfig = slaConfigOpt.get();
        LocalDateTime createdAt = issue.getCreatedAt();

        SlaTracking slaTracking = SlaTracking.builder()
                .issue(issue)
                .slaConfiguration(slaConfig)
                .responseDueDate(slaConfig.calculateResponseDueDate(createdAt))
                .resolutionDueDate(slaConfig.calculateResolutionDueDate(createdAt))
                .escalationDueDate(slaConfig.calculateEscalationDueDate(createdAt))
                .slaStatus("ACTIVE")
                .build();

        return slaTrackingRepository.save(slaTracking);
    }

    @Override
    public SlaTracking updateSlaTracking(SlaTracking slaTracking) {
        log.debug("Updating SLA tracking: {}", slaTracking.getId());
        return slaTrackingRepository.save(slaTracking);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SlaTracking> getSlaTrackingByIssue(Issue issue) {
        return slaTrackingRepository.findByIssue(issue);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<SlaTracking> getSlaTrackingByIssueId(Long issueId) {
        return slaTrackingRepository.findByIssueId(issueId);
    }

    @Override
    public void markResponseCompleted(Long issueId) {
        log.debug("Marking response completed for issue ID: {}", issueId);
        
        Optional<SlaTracking> slaTrackingOpt = getSlaTrackingByIssueId(issueId);
        if (slaTrackingOpt.isPresent()) {
            SlaTracking slaTracking = slaTrackingOpt.get();
            slaTracking.markResponseCompleted();
            slaTrackingRepository.save(slaTracking);
            
            log.info("Response completed for issue ID: {} at {}", issueId, slaTracking.getResponseDate());
        }
    }

    @Override
    public void markResolutionCompleted(Long issueId) {
        log.debug("Marking resolution completed for issue ID: {}", issueId);
        
        Optional<SlaTracking> slaTrackingOpt = getSlaTrackingByIssueId(issueId);
        if (slaTrackingOpt.isPresent()) {
            SlaTracking slaTracking = slaTrackingOpt.get();
            slaTracking.markResolutionCompleted();
            slaTrackingRepository.save(slaTracking);
            
            log.info("Resolution completed for issue ID: {} at {}", issueId, slaTracking.getResolutionDate());
        }
    }

    @Override
    public void checkSlaBreachesAndEscalate() {
        log.debug("Checking for SLA breaches and escalations");
        
        LocalDateTime now = LocalDateTime.now();
        
        // Check for response time breaches
        List<SlaTracking> responseBreaches = slaTrackingRepository.findResponseBreached(now);
        for (SlaTracking slaTracking : responseBreaches) {
            handleResponseBreach(slaTracking);
        }
        
        // Check for resolution time breaches
        List<SlaTracking> resolutionBreaches = slaTrackingRepository.findResolutionBreached(now);
        for (SlaTracking slaTracking : resolutionBreaches) {
            handleResolutionBreach(slaTracking);
        }
        
        // Check for escalations due
        List<SlaTracking> escalationsDue = slaTrackingRepository.findEscalationDue(now);
        for (SlaTracking slaTracking : escalationsDue) {
            handleEscalation(slaTracking);
        }
        
        log.info("SLA breach check completed. Response breaches: {}, Resolution breaches: {}, Escalations: {}", 
                responseBreaches.size(), resolutionBreaches.size(), escalationsDue.size());
    }

    private void handleResponseBreach(SlaTracking slaTracking) {
        log.warn("Response SLA breach detected for issue: {}", slaTracking.getIssue().getIdentifier());
        
        // Update SLA status
        slaTracking.setSlaStatus("RESPONSE_BREACHED");
        slaTracking.setBreachReason("Response time exceeded");
        slaTrackingRepository.save(slaTracking);
        
        // Send notification to assignee and manager
        if (slaTracking.getIssue().getAssignee() != null) {
            notificationService.createDeadlineApproachingNotification(
                    slaTracking.getIssue(), slaTracking.getIssue().getAssignee());
        }
    }

    private void handleResolutionBreach(SlaTracking slaTracking) {
        log.warn("Resolution SLA breach detected for issue: {}", slaTracking.getIssue().getIdentifier());
        
        // Update SLA status
        slaTracking.setSlaStatus("RESOLUTION_BREACHED");
        slaTracking.setBreachReason("Resolution time exceeded");
        slaTrackingRepository.save(slaTracking);
        
        // Send notification to assignee and manager
        if (slaTracking.getIssue().getAssignee() != null) {
            notificationService.createDeadlineApproachingNotification(
                    slaTracking.getIssue(), slaTracking.getIssue().getAssignee());
        }
    }

    private void handleEscalation(SlaTracking slaTracking) {
        log.warn("Escalation due for issue: {}", slaTracking.getIssue().getIdentifier());
        
        // Mark escalation as triggered
        slaTracking.markEscalationTriggered("Escalation time exceeded");
        slaTrackingRepository.save(slaTracking);
        
        // Send escalation notification
        if (slaTracking.getIssue().getAssignee() != null) {
            notificationService.createDeadlineApproachingNotification(
                    slaTracking.getIssue(), slaTracking.getIssue().getAssignee());
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<SlaTracking> getAllSlaBreaches() {
        LocalDateTime now = LocalDateTime.now();
        List<SlaTracking> responseBreaches = slaTrackingRepository.findResponseBreached(now);
        List<SlaTracking> resolutionBreaches = slaTrackingRepository.findResolutionBreached(now);
        
        responseBreaches.addAll(resolutionBreaches);
        return responseBreaches;
    }

    @Override
    @Transactional(readOnly = true)
    public List<SlaTracking> getResponseTimeBreaches() {
        return slaTrackingRepository.findResponseBreached(LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SlaTracking> getResolutionTimeBreaches() {
        return slaTrackingRepository.findResolutionBreached(LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public List<SlaTracking> getEscalationDue() {
        return slaTrackingRepository.findEscalationDue(LocalDateTime.now());
    }

    @Override
    @Transactional(readOnly = true)
    public SlaStatistics getSlaStatistics() {
        LocalDateTime now = LocalDateTime.now();
        
        long totalSlas = slaTrackingRepository.count();
        long activeSlas = slaTrackingRepository.countBySlaStatus("ACTIVE");
        long completedSlas = slaTrackingRepository.countBySlaStatus("COMPLETED");
        long responseBreaches = slaTrackingRepository.countResponseBreaches(now);
        long resolutionBreaches = slaTrackingRepository.countResolutionBreaches(now);
        long escalatedSlas = slaTrackingRepository.countBySlaStatus("ESCALATED");
        
        // Calculate average times using database-agnostic approach
        Double averageResponseTime = calculateAverageResponseTimeHours();
        Double averageResolutionTime = calculateAverageResolutionTimeHours();
        
        Double compliancePercentage = 0.0;
        if (totalSlas > 0) {
            long totalBreaches = responseBreaches + resolutionBreaches;
            compliancePercentage = ((double) (totalSlas - totalBreaches) / totalSlas) * 100;
        }
        
        return new SlaStatistics(totalSlas, activeSlas, completedSlas, responseBreaches, 
                resolutionBreaches, escalatedSlas, averageResponseTime, averageResolutionTime, compliancePercentage);
    }

    @Override
    @Transactional(readOnly = true)
    public SlaStatistics getSlaStatistics(LocalDateTime startDate, LocalDateTime endDate) {
        List<SlaTracking> slaTrackings = slaTrackingRepository.findByCreatedAtBetween(startDate, endDate);
        
        long totalSlas = slaTrackings.size();
        long activeSlas = slaTrackings.stream().mapToLong(st -> "ACTIVE".equals(st.getSlaStatus()) ? 1 : 0).sum();
        long completedSlas = slaTrackings.stream().mapToLong(st -> "COMPLETED".equals(st.getSlaStatus()) ? 1 : 0).sum();
        long responseBreaches = slaTrackings.stream().mapToLong(st -> st.isResponseBreached() ? 1 : 0).sum();
        long resolutionBreaches = slaTrackings.stream().mapToLong(st -> st.isResolutionBreached() ? 1 : 0).sum();
        long escalatedSlas = slaTrackings.stream().mapToLong(st -> st.getEscalationTriggered() ? 1 : 0).sum();
        
        Double averageResponseTime = slaTrackings.stream()
                .filter(st -> st.getResponseTimeHours() != null)
                .mapToDouble(st -> st.getResponseTimeHours().doubleValue())
                .average().orElse(0.0);
        
        Double averageResolutionTime = slaTrackings.stream()
                .filter(st -> st.getResolutionTimeHours() != null)
                .mapToDouble(st -> st.getResolutionTimeHours().doubleValue())
                .average().orElse(0.0);
        
        Double compliancePercentage = 0.0;
        if (totalSlas > 0) {
            long totalBreaches = responseBreaches + resolutionBreaches;
            compliancePercentage = ((double) (totalSlas - totalBreaches) / totalSlas) * 100;
        }
        
        return new SlaStatistics(totalSlas, activeSlas, completedSlas, responseBreaches, 
                resolutionBreaches, escalatedSlas, averageResponseTime, averageResolutionTime, compliancePercentage);
    }

    @Override
    @Transactional(readOnly = true)
    public Double getSlaCompliancePercentage() {
        return getSlaStatistics().getCompliancePercentage();
    }

    @Override
    @Transactional(readOnly = true)
    public Double getAverageResponseTime() {
        return calculateAverageResponseTimeHours();
    }

    @Override
    @Transactional(readOnly = true)
    public Double getAverageResolutionTime() {
        return calculateAverageResolutionTimeHours();
    }

    @Override
    @Transactional(readOnly = true)
    public List<SlaTracking> getSlaTrackingsByStatus(String status) {
        return slaTrackingRepository.findBySlaStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SlaTracking> getUpcomingSlaDeadlines(int hours) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureTime = now.plusHours(hours);
        
        List<SlaTracking> upcomingResponse = slaTrackingRepository.findResponseDueWithin(now, futureTime);
        List<SlaTracking> upcomingResolution = slaTrackingRepository.findResolutionDueWithin(now, futureTime);
        
        upcomingResponse.addAll(upcomingResolution);
        return upcomingResponse;
    }

    /**
     * Calculate average response time in hours using database-agnostic approach
     */
    private Double calculateAverageResponseTimeHours() {
        List<SlaTracking> completedSlas = slaTrackingRepository.findCompletedResponseSlas();
        return completedSlas.stream()
                .filter(st -> st.getResponseTimeHours() != null)
                .mapToDouble(st -> st.getResponseTimeHours().doubleValue())
                .average()
                .orElse(0.0);
    }

    /**
     * Calculate average resolution time in hours using database-agnostic approach
     */
    private Double calculateAverageResolutionTimeHours() {
        List<SlaTracking> completedSlas = slaTrackingRepository.findCompletedResolutionSlas();
        return completedSlas.stream()
                .filter(st -> st.getResolutionTimeHours() != null)
                .mapToDouble(st -> st.getResolutionTimeHours().doubleValue())
                .average()
                .orElse(0.0);
    }
}
