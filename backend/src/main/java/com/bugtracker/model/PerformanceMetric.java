package com.bugtracker.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Performance Metric entity for tracking system and user performance
 */
@Entity
@Table(name = "performance_metrics")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class)
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class PerformanceMetric {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "metric_type", nullable = false, length = 50)
    private String metricType;

    @Column(name = "metric_name", nullable = false, length = 100)
    private String metricName;

    @Column(name = "metric_value", nullable = false, precision = 10, scale = 4)
    private BigDecimal metricValue;

    @Column(nullable = false, length = 20)
    private String unit;

    @Column(name = "context_data", columnDefinition = "TEXT")
    @Convert(converter = com.bugtracker.config.JsonNodeAttributeConverter.class)
    private JsonNode contextData;

    @Column(name = "recorded_at", nullable = false)
    @Builder.Default
    private LocalDateTime recordedAt = LocalDateTime.now();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "roles", "password"})
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "issue_id")
    @JsonIgnoreProperties({"hibernateLazyInitializer", "handler", "performanceMetrics"})
    private Issue issue;

    /**
     * Metric types enum for validation
     */
    public enum MetricType {
        API_RESPONSE_TIME("API Response Time"),
        DB_QUERY_TIME("Database Query Time"),
        AI_PROCESSING_TIME("AI Processing Time"),
        VOICE_RECOGNITION_TIME("Voice Recognition Time"),
        USER_SESSION_DURATION("User Session Duration"),
        ISSUE_CREATION_TIME("Issue Creation Time"),
        SEARCH_RESPONSE_TIME("Search Response Time"),
        DASHBOARD_LOAD_TIME("Dashboard Load Time"),
        NOTIFICATION_DELIVERY_TIME("Notification Delivery Time"),
        FILE_UPLOAD_TIME("File Upload Time"),
        EXPORT_GENERATION_TIME("Export Generation Time"),
        LOGIN_TIME("Login Time"),
        PAGE_LOAD_TIME("Page Load Time"),
        MEMORY_USAGE("Memory Usage"),
        CPU_USAGE("CPU Usage"),
        DISK_USAGE("Disk Usage"),
        NETWORK_LATENCY("Network Latency"),
        ERROR_RATE("Error Rate"),
        THROUGHPUT("Throughput"),
        CONCURRENT_USERS("Concurrent Users");

        private final String displayName;

        MetricType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * Unit types enum for validation
     */
    public enum Unit {
        MILLISECONDS("ms"),
        SECONDS("s"),
        MINUTES("min"),
        HOURS("h"),
        PERCENTAGE("%"),
        COUNT("count"),
        BYTES("bytes"),
        KILOBYTES("KB"),
        MEGABYTES("MB"),
        GIGABYTES("GB"),
        REQUESTS_PER_SECOND("req/s"),
        QUERIES_PER_SECOND("qps"),
        TRANSACTIONS_PER_SECOND("tps");

        private final String symbol;

        Unit(String symbol) {
            this.symbol = symbol;
        }

        public String getSymbol() {
            return symbol;
        }
    }

    /**
     * Create a performance metric for API response time
     */
    public static PerformanceMetric createApiResponseTime(String endpoint, BigDecimal responseTime, User user) {
        return PerformanceMetric.builder()
                .metricType(MetricType.API_RESPONSE_TIME.name())
                .metricName("API Response Time - " + endpoint)
                .metricValue(responseTime)
                .unit(Unit.MILLISECONDS.getSymbol())
                .user(user)
                .build();
    }

    /**
     * Create a performance metric for database query time
     */
    public static PerformanceMetric createDbQueryTime(String queryType, BigDecimal queryTime) {
        return PerformanceMetric.builder()
                .metricType(MetricType.DB_QUERY_TIME.name())
                .metricName("DB Query Time - " + queryType)
                .metricValue(queryTime)
                .unit(Unit.MILLISECONDS.getSymbol())
                .build();
    }

    /**
     * Create a performance metric for AI processing time
     */
    public static PerformanceMetric createAiProcessingTime(String aiOperation, BigDecimal processingTime, Issue issue) {
        return PerformanceMetric.builder()
                .metricType(MetricType.AI_PROCESSING_TIME.name())
                .metricName("AI Processing Time - " + aiOperation)
                .metricValue(processingTime)
                .unit(Unit.MILLISECONDS.getSymbol())
                .issue(issue)
                .build();
    }

    /**
     * Create a performance metric for user session duration
     */
    public static PerformanceMetric createUserSessionDuration(BigDecimal sessionDuration, User user) {
        return PerformanceMetric.builder()
                .metricType(MetricType.USER_SESSION_DURATION.name())
                .metricName("User Session Duration")
                .metricValue(sessionDuration)
                .unit(Unit.MINUTES.getSymbol())
                .user(user)
                .build();
    }

    /**
     * Get formatted metric value with unit
     */
    public String getFormattedValue() {
        return metricValue.toString() + " " + unit;
    }

    /**
     * Check if metric indicates good performance
     */
    public boolean isGoodPerformance() {
        switch (MetricType.valueOf(metricType)) {
            case API_RESPONSE_TIME:
            case DB_QUERY_TIME:
            case AI_PROCESSING_TIME:
                return metricValue.compareTo(BigDecimal.valueOf(1000)) <= 0; // < 1 second
            case PAGE_LOAD_TIME:
                return metricValue.compareTo(BigDecimal.valueOf(3000)) <= 0; // < 3 seconds
            case ERROR_RATE:
                return metricValue.compareTo(BigDecimal.valueOf(5)) <= 0; // < 5%
            case CPU_USAGE:
            case MEMORY_USAGE:
                return metricValue.compareTo(BigDecimal.valueOf(80)) <= 0; // < 80%
            default:
                return true;
        }
    }
}
