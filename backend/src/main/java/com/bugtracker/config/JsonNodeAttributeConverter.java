package com.bugtracker.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import lombok.extern.slf4j.Slf4j;

/**
 * Production-grade JPA AttributeConverter for JsonNode to handle JSON data
 * in a database-agnostic way. Converts JsonNode objects to JSON strings
 * for database storage and vice versa.
 *
 * Note: This converter does not use Spring dependency injection to avoid
 * circular dependency issues during JPA initialization.
 */
@Converter
@Slf4j
public class JsonNodeAttributeConverter implements AttributeConverter<JsonNode, String> {

    private static final ObjectMapper objectMapper = createObjectMapper();

    /**
     * Create a static ObjectMapper instance to avoid dependency injection issues
     */
    private static ObjectMapper createObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }

    /**
     * Convert JsonNode to database column (JSON string)
     */
    @Override
    public String convertToDatabaseColumn(JsonNode attribute) {
        if (attribute == null) {
            return null;
        }

        try {
            String json = objectMapper.writeValueAsString(attribute);
            log.debug("Converting JsonNode to database column: {}", json);
            return json;
        } catch (JsonProcessingException e) {
            log.error("Error converting JsonNode to JSON string", e);
            throw new RuntimeException("Error converting JsonNode to JSON string", e);
        }
    }

    /**
     * Convert database column (JSON string) to JsonNode
     */
    @Override
    public JsonNode convertToEntityAttribute(String dbData) {
        if (dbData == null || dbData.trim().isEmpty()) {
            return null;
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(dbData);
            log.debug("Converting database column to JsonNode: {}", dbData);
            return jsonNode;
        } catch (JsonProcessingException e) {
            log.error("Error converting JSON string to JsonNode: {}", dbData, e);
            throw new RuntimeException("Error converting JSON string to JsonNode", e);
        }
    }
}
