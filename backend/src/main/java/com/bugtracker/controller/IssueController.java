package com.bugtracker.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import com.bugtracker.model.Issue;
import com.bugtracker.model.User;
import com.bugtracker.model.Label;
import com.bugtracker.payload.response.MessageResponse;
import com.bugtracker.service.IssueService;
import com.bugtracker.service.UserService;
import com.bugtracker.service.LabelService;
import com.bugtracker.service.AIIssueAssistService;
import com.bugtracker.dto.AIIssueAssistRequest;
import com.bugtracker.dto.AIIssueAssistResponse;
import com.bugtracker.exception.IssueIdentifierConflictException;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/issues")
public class IssueController {

    @Autowired
    private IssueService issueService;

    @Autowired
    private UserService userService;

    @Autowired
    private LabelService labelService;

    @Autowired
    private AIIssueAssistService aiIssueAssistService;

    @GetMapping
    public ResponseEntity<Page<Issue>> getAllIssues(
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String priority,
            @RequestParam(required = false) String environment,
            @RequestParam(required = false) Boolean overdue,
            Pageable pageable) {

        if (overdue != null && overdue) {
            // Return overdue issues as a page
            List<Issue> overdueIssues = issueService.getOverdueIssues();
            return ResponseEntity.ok(issueService.convertListToPage(overdueIssues, pageable));
        }

        Page<Issue> issues = issueService.getFilteredIssues(search, status, type, priority, environment, pageable);
        return ResponseEntity.ok(issues);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getIssueById(@PathVariable Long id) {
        try {
            return issueService.getIssueByIdWithDetails(id)
                    .map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }

    @GetMapping("/{id}/test")
    public ResponseEntity<?> testIssueById(@PathVariable Long id) {
        try {
            Optional<Issue> issueOpt = issueService.getIssueByIdWithDetails(id);
            if (issueOpt.isPresent()) {
                Issue issue = issueOpt.get();
                return ResponseEntity.ok("Issue found: " + issue.getTitle() +
                    ", Comments: " + issue.getComments().size() +
                    ", Attachments: " + issue.getAttachments().size());
            }
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }

    @GetMapping("/identifier/{identifier}")
    public ResponseEntity<?> getIssueByIdentifier(@PathVariable String identifier) {
        try {
            return issueService.getIssueByIdentifierWithDetails(identifier)
                    .map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(500).body("Error: " + e.getMessage());
        }
    }

    @GetMapping("/assignee/{id}")
    public ResponseEntity<Page<Issue>> getIssuesByAssignee(@PathVariable Long id, Pageable pageable) {
        return userService.getUserById(id)
                .map(user -> ResponseEntity.ok(issueService.getIssuesByAssignee(user, pageable)))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/reporter/{id}")
    public ResponseEntity<Page<Issue>> getIssuesByReporter(@PathVariable Long id, Pageable pageable) {
        return userService.getUserById(id)
                .map(user -> ResponseEntity.ok(issueService.getIssuesByReporter(user, pageable)))
                .orElse(ResponseEntity.notFound().build());
    }



    @GetMapping("/overdue")
    public ResponseEntity<List<Issue>> getOverdueIssues() {
        List<Issue> issues = issueService.getOverdueIssues();
        return ResponseEntity.ok(issues);
    }

    @GetMapping("/recent")
    public ResponseEntity<List<Issue>> getRecentIssues(@RequestParam(required = false, defaultValue = "5") int limit) {
        List<Issue> issues = issueService.getRecentIssues(limit);
        return ResponseEntity.ok(issues);
    }

    @GetMapping("/stats/dashboard")
    public ResponseEntity<?> getDashboardStats(
            @RequestParam(required = false, defaultValue = "current_week") String period,
            @RequestParam(required = false) String environment) {
        // Create a dashboard stats object with counts
        var stats = issueService.getDashboardStats(period, environment);
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/active/assignee/{id}")
    public ResponseEntity<List<Issue>> getActiveIssuesByAssignee(@PathVariable Long id) {
        return userService.getUserById(id)
                .map(user -> ResponseEntity.ok(issueService.getActiveIssuesByAssignee(user)))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/children/{parentId}")
    public ResponseEntity<List<Issue>> getChildIssues(@PathVariable Long parentId) {
        return issueService.getIssueById(parentId)
                .map(parent -> ResponseEntity.ok(issueService.getChildIssues(parent)))
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/root")
    public ResponseEntity<Page<Issue>> getRootIssues(Pageable pageable) {
        Page<Issue> issues = issueService.getRootIssues(pageable);
        return ResponseEntity.ok(issues);
    }

    @GetMapping("/search")
    public ResponseEntity<Page<Issue>> searchIssues(@RequestParam String keyword, Pageable pageable) {
        Page<Issue> issues = issueService.searchIssues(keyword, pageable);
        return ResponseEntity.ok(issues);
    }



    @PostMapping
    public ResponseEntity<?> createIssue(@RequestBody Issue issue) {
        try {
            // Try to get the admin user by username
            Optional<User> adminUser = userService.getUserByUsername("admin");

            if (adminUser.isPresent()) {
                issue.setReporter(adminUser.get());
            } else {
                // Try to use any available user as the reporter
                List<User> users = userService.getAllUsers();

                if (users.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(new com.bugtracker.exception.ErrorResponse(
                            new Date(),
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            "No users available to assign as reporter",
                            "/api/issues"
                        ));
                } else {
                    // Use the first available user
                    issue.setReporter(users.get(0));
                }
            }

            // Note: Removed manual identifier check as the service now handles this with retry logic
            // The service will automatically generate unique identifiers and handle conflicts

            Issue createdIssue = issueService.createIssue(issue);
            return ResponseEntity.ok(createdIssue);
        } catch (IllegalArgumentException e) {
            // Handle validation errors
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(new com.bugtracker.exception.ErrorResponse(
                    new Date(),
                    HttpStatus.BAD_REQUEST.value(),
                    "Validation error: " + e.getMessage(),
                    "/api/issues"
                ));
        } catch (IssueIdentifierConflictException e) {
            // Handle specific identifier conflict errors
            return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(new com.bugtracker.exception.ErrorResponse(
                    new Date(),
                    HttpStatus.CONFLICT.value(),
                    e.getMessage(),
                    "/api/issues"
                ));
        } catch (RuntimeException e) {
            // Handle constraint violations and other runtime errors
            String message = e.getMessage();
            if (message != null && (message.contains("unique identifier") ||
                                  message.contains("constraint violation") ||
                                  message.contains("duplicate key"))) {
                return ResponseEntity.status(HttpStatus.CONFLICT)
                    .body(new com.bugtracker.exception.ErrorResponse(
                        new Date(),
                        HttpStatus.CONFLICT.value(),
                        "Unable to create issue due to identifier conflict. Please try again.",
                        "/api/issues"
                    ));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new com.bugtracker.exception.ErrorResponse(
                        new Date(),
                        HttpStatus.INTERNAL_SERVER_ERROR.value(),
                        "Failed to create issue: " + message,
                        "/api/issues"
                    ));
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new com.bugtracker.exception.ErrorResponse(
                    new Date(),
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    "An unexpected error occurred while creating the issue. Please try again.",
                    "/api/issues"
                ));
        }
    }

    @PostMapping("/bulk")
    public ResponseEntity<?> bulkCreateIssues(@RequestBody Map<String, List<Map<String, Object>>> requestBody) {
        try {
            List<Map<String, Object>> issuesData = requestBody.get("issues");
            if (issuesData == null || issuesData.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new com.bugtracker.exception.ErrorResponse(
                        new Date(),
                        HttpStatus.BAD_REQUEST.value(),
                        "No issues data provided",
                        "/api/issues/bulk"
                    ));
            }

            Map<String, Object> result = issueService.bulkCreateIssues(issuesData);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new com.bugtracker.exception.ErrorResponse(
                    new Date(),
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    e.getMessage(),
                    "/api/issues/bulk"
                ));
        }
    }

    @PostMapping("/ai-assist")
    public ResponseEntity<?> getAIAssistance(@RequestBody AIIssueAssistRequest request) {
        try {
            if (!aiIssueAssistService.isAIAssistEnabled()) {
                return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                    .body(new com.bugtracker.exception.ErrorResponse(
                        new Date(),
                        HttpStatus.SERVICE_UNAVAILABLE.value(),
                        "AI assistance is not enabled",
                        "/api/issues/ai-assist"
                    ));
            }

            AIIssueAssistResponse response = aiIssueAssistService.generateIssueDetails(request);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new com.bugtracker.exception.ErrorResponse(
                    new Date(),
                    HttpStatus.INTERNAL_SERVER_ERROR.value(),
                    e.getMessage(),
                    "/api/issues/ai-assist"
                ));
        }
    }

    @PutMapping("/{id}")
    public ResponseEntity<?> updateIssue(@PathVariable Long id, @RequestBody Issue issueDetails) {
        return issueService.getIssueById(id)
                .map(issue -> {
                    issue.setTitle(issueDetails.getTitle());
                    issue.setDescription(issueDetails.getDescription());
                    issue.setType(issueDetails.getType());
                    issue.setSeverity(issueDetails.getSeverity());
                    issue.setPriority(issueDetails.getPriority());
                    issue.setStatus(issueDetails.getStatus());
                    issue.setAssignee(issueDetails.getAssignee());
                    issue.setDevCompletionDate(issueDetails.getDevCompletionDate());
                    issue.setQcCompletionDate(issueDetails.getQcCompletionDate());
                    issue.setRootCause(issueDetails.getRootCause());
                    issue.setCompletionDate(issueDetails.getCompletionDate());
                    issue.setParent(issueDetails.getParent());
                    issue.setVersionRelease(issueDetails.getVersionRelease());
                    issue.setEnvironment(issueDetails.getEnvironment());

                    Issue updatedIssue = issueService.updateIssue(issue);
                    return ResponseEntity.ok(updatedIssue);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteIssue(@PathVariable Long id) {
        return issueService.getIssueById(id)
                .map(issue -> {
                    issueService.deleteIssue(id);
                    return ResponseEntity.ok(new MessageResponse("Issue deleted successfully"));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/{issueId}/watchers/{userId}")
    public ResponseEntity<?> addWatcher(@PathVariable Long issueId, @PathVariable Long userId) {
        issueService.addWatcher(issueId, userId);
        return ResponseEntity.ok(new MessageResponse("Watcher added successfully"));
    }

    @DeleteMapping("/{issueId}/watchers/{userId}")
    public ResponseEntity<?> removeWatcher(@PathVariable Long issueId, @PathVariable Long userId) {
        issueService.removeWatcher(issueId, userId);
        return ResponseEntity.ok(new MessageResponse("Watcher removed successfully"));
    }

    @PostMapping("/{issueId}/labels/{labelId}")
    public ResponseEntity<?> addLabel(@PathVariable Long issueId, @PathVariable Long labelId) {
        try {
            issueService.addLabel(issueId, labelId);
            return ResponseEntity.ok(new MessageResponse("Label added successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }

    @DeleteMapping("/{issueId}/labels/{labelId}")
    public ResponseEntity<?> removeLabel(@PathVariable Long issueId, @PathVariable Long labelId) {
        try {
            issueService.removeLabel(issueId, labelId);
            return ResponseEntity.ok(new MessageResponse("Label removed successfully"));
        } catch (RuntimeException e) {
            return ResponseEntity.badRequest().body(new MessageResponse(e.getMessage()));
        }
    }
}
