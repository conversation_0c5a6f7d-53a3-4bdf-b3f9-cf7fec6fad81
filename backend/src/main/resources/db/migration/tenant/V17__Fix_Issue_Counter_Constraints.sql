-- V17__Fix_Issue_Counter_Constraints.sql
-- Fix issue counter system to prevent duplicate identifier constraint violations
-- This migration ensures proper counter initialization and constraint handling

-- Ensure issue_counters table has the correct structure
DO $$
BEGIN
    -- Check if issue_counters table exists and has the expected structure
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'issue_counters') THEN
        
        -- Check if we have the old structure (issue_type, last_used_counter)
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'issue_counters' AND column_name = 'issue_type') THEN
            RAISE NOTICE 'Issue counters table has correct structure (issue_type, last_used_counter)';
        ELSE
            -- We have the new structure, need to migrate to old structure for compatibility
            RAISE NOTICE 'Migrating issue_counters table to compatible structure';
            
            -- Create temporary table with old structure
            CREATE TABLE IF NOT EXISTS issue_counters_temp (
                issue_type VARCHAR(50) PRIMARY KEY,
                last_used_counter INTEGER NOT NULL DEFAULT 0
            );
            
            -- Migrate data from new structure to old structure
            INSERT INTO issue_counters_temp (issue_type, last_used_counter)
            SELECT counter_type, current_value 
            FROM issue_counters
            ON CONFLICT (issue_type) DO UPDATE SET 
                last_used_counter = GREATEST(issue_counters_temp.last_used_counter, EXCLUDED.last_used_counter);
            
            -- Drop old table and rename temp table
            DROP TABLE issue_counters;
            ALTER TABLE issue_counters_temp RENAME TO issue_counters;
        END IF;
        
    ELSE
        -- Create issue_counters table with correct structure
        RAISE NOTICE 'Creating issue_counters table';
        CREATE TABLE issue_counters (
            issue_type VARCHAR(50) PRIMARY KEY,
            last_used_counter INTEGER NOT NULL DEFAULT 0
        );
    END IF;
END $$;

-- Initialize counters for all existing issue types from lookup_values
-- This ensures any new issue types added to lookup_values will work automatically
DO $$
DECLARE
    issue_type_record RECORD;
    max_counter INTEGER;
    prefix_pattern TEXT;
BEGIN
    -- Loop through all active issue types in lookup_values
    FOR issue_type_record IN 
        SELECT code, display_name 
        FROM lookup_values 
        WHERE category = 'ISSUE_TYPE' AND is_active = true
    LOOP
        -- Determine the prefix pattern for this issue type
        CASE issue_type_record.code
            WHEN 'BUG' THEN prefix_pattern := 'BUG-%';
            WHEN 'FEATURE' THEN prefix_pattern := 'FEAT-%';
            WHEN 'ENHANCEMENT' THEN prefix_pattern := 'ENH-%';
            WHEN 'TASK' THEN prefix_pattern := 'TASK-%';
            WHEN 'STORY' THEN prefix_pattern := 'STORY-%';
            WHEN 'EPIC' THEN prefix_pattern := 'EPIC-%';
            WHEN 'SPIKE' THEN prefix_pattern := 'SPIKE-%';
            ELSE 
                -- For any new types, use the first 4-6 characters or the full type if short
                IF LENGTH(issue_type_record.code) <= 6 THEN
                    prefix_pattern := UPPER(issue_type_record.code) || '-%';
                ELSE
                    prefix_pattern := UPPER(SUBSTRING(issue_type_record.code FROM 1 FOR 4)) || '-%';
                END IF;
        END CASE;
        
        -- Find the maximum counter value for this issue type from existing issues
        SELECT COALESCE(MAX(
            CASE 
                WHEN identifier ~ '^[A-Z]+-[0-9]+$' THEN 
                    CAST(SUBSTRING(identifier FROM '[0-9]+$') AS INTEGER)
                ELSE 0
            END
        ), 0) INTO max_counter
        FROM issues 
        WHERE identifier LIKE prefix_pattern;
        
        RAISE NOTICE 'Issue type: %, Pattern: %, Max counter: %', issue_type_record.code, prefix_pattern, max_counter;
        
        -- Insert or update the counter for this issue type
        INSERT INTO issue_counters (issue_type, last_used_counter)
        VALUES (issue_type_record.code, max_counter)
        ON CONFLICT (issue_type) 
        DO UPDATE SET last_used_counter = GREATEST(issue_counters.last_used_counter, EXCLUDED.last_used_counter);
        
    END LOOP;
    
    RAISE NOTICE 'Issue counter initialization completed';
END $$;

-- Create a function to automatically initialize counters for new issue types
-- This function will be called whenever a new issue type is added to lookup_values
CREATE OR REPLACE FUNCTION initialize_issue_type_counter()
RETURNS TRIGGER AS $$
BEGIN
    -- Only process ISSUE_TYPE category
    IF NEW.category = 'ISSUE_TYPE' AND NEW.is_active = true THEN
        -- Initialize counter for new issue type
        INSERT INTO issue_counters (issue_type, last_used_counter)
        VALUES (NEW.code, 0)
        ON CONFLICT (issue_type) DO NOTHING;
        
        RAISE NOTICE 'Auto-initialized counter for new issue type: %', NEW.code;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically initialize counters for new issue types
DROP TRIGGER IF EXISTS trigger_initialize_issue_type_counter ON lookup_values;
CREATE TRIGGER trigger_initialize_issue_type_counter
    AFTER INSERT OR UPDATE ON lookup_values
    FOR EACH ROW
    EXECUTE FUNCTION initialize_issue_type_counter();

-- Add index on issues.identifier for better performance
CREATE INDEX IF NOT EXISTS idx_issues_identifier ON issues(identifier);

-- Add comments for documentation
COMMENT ON TABLE issue_counters IS 'Counter system for generating unique issue identifiers per issue type';
COMMENT ON FUNCTION initialize_issue_type_counter() IS 'Automatically initializes counters for new issue types added to lookup_values';
COMMENT ON TRIGGER trigger_initialize_issue_type_counter ON lookup_values IS 'Trigger to auto-initialize counters for new issue types';

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Issue counter constraint fix migration completed successfully';
    RAISE NOTICE 'The system now has enhanced protection against duplicate identifier violations';
END $$;
