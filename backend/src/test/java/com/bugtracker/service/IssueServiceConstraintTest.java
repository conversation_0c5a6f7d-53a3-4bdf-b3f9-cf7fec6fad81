package com.bugtracker.service;

import com.bugtracker.model.Issue;
import com.bugtracker.model.User;
import com.bugtracker.service.impl.IssueServiceImpl;
import com.bugtracker.repository.IssueRepository;
import com.bugtracker.repository.IssueCounterRepository;
import com.bugtracker.repository.UserRepository;
import com.bugtracker.repository.LabelRepository;
import com.bugtracker.service.NotificationService;
import com.bugtracker.service.ValidationService;
import com.bugtracker.service.SlaService;
import com.bugtracker.exception.IssueIdentifierConflictException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.CountDownLatch;
import java.util.List;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class for verifying the issue creation constraint violation fixes
 * Tests the retry logic and unique identifier generation
 */
@ExtendWith(MockitoExtension.class)
public class IssueServiceConstraintTest {

    @Mock
    private IssueRepository issueRepository;
    
    @Mock
    private IssueCounterRepository issueCounterRepository;
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private LabelRepository labelRepository;
    
    @Mock
    private NotificationService notificationService;
    
    @Mock
    private ValidationService validationService;
    
    @Mock
    private SlaService slaService;

    private IssueServiceImpl issueService;

    @BeforeEach
    void setUp() {
        issueService = new IssueServiceImpl(
            issueRepository,
            userRepository,
            labelRepository,
            issueCounterRepository,
            notificationService,
            validationService,
            slaService
        );
    }

    @Test
    void testCreateIssue_Success() {
        // Arrange
        Issue issue = createTestIssue();
        User reporter = createTestUser();
        issue.setReporter(reporter);

        when(validationService.isValidLookupValue(anyString(), anyString())).thenReturn(true);
        when(issueRepository.findByIdentifier(anyString())).thenReturn(Optional.empty());
        when(issueRepository.save(any(Issue.class))).thenReturn(issue);

        // Act
        Issue result = issueService.createIssue(issue);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getIdentifier());
        verify(issueRepository, times(1)).save(any(Issue.class));
    }

    @Test
    void testCreateIssue_ConstraintViolationRetry() {
        // Arrange
        Issue issue = createTestIssue();
        User reporter = createTestUser();
        issue.setReporter(reporter);

        when(validationService.isValidLookupValue(anyString(), anyString())).thenReturn(true);
        
        // First call returns existing issue (conflict), second call returns empty (success)
        when(issueRepository.findByIdentifier(anyString()))
            .thenReturn(Optional.of(new Issue())) // First attempt - conflict
            .thenReturn(Optional.empty());        // Second attempt - success
        
        when(issueRepository.save(any(Issue.class))).thenReturn(issue);

        // Act
        Issue result = issueService.createIssue(issue);

        // Assert
        assertNotNull(result);
        verify(issueRepository, times(2)).findByIdentifier(anyString()); // Called twice due to retry
        verify(issueRepository, times(1)).save(any(Issue.class));
    }

    @Test
    void testCreateIssue_MaxRetriesExceeded() {
        // Arrange
        Issue issue = createTestIssue();
        User reporter = createTestUser();
        issue.setReporter(reporter);

        when(validationService.isValidLookupValue(anyString(), anyString())).thenReturn(true);
        
        // Always return existing issue (persistent conflict)
        when(issueRepository.findByIdentifier(anyString())).thenReturn(Optional.of(new Issue()));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            issueService.createIssue(issue);
        });
    }

    @Test
    void testCreateIssue_DatabaseConstraintViolation() {
        // Arrange
        Issue issue = createTestIssue();
        User reporter = createTestUser();
        issue.setReporter(reporter);

        when(validationService.isValidLookupValue(anyString(), anyString())).thenReturn(true);
        when(issueRepository.findByIdentifier(anyString())).thenReturn(Optional.empty());
        
        // First save throws constraint violation, second succeeds
        when(issueRepository.save(any(Issue.class)))
            .thenThrow(new DataIntegrityViolationException("duplicate key constraint [uk3tytaluobgb0byoipk88tbxlr]"))
            .thenReturn(issue);

        // Act
        Issue result = issueService.createIssue(issue);

        // Assert
        assertNotNull(result);
        verify(issueRepository, times(2)).save(any(Issue.class)); // Called twice due to retry
    }

    @Test
    void testConcurrentIssueCreation() throws Exception {
        // Arrange
        int numberOfThreads = 10;
        ExecutorService executor = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch latch = new CountDownLatch(numberOfThreads);
        List<Future<Issue>> futures = new ArrayList<>();

        when(validationService.isValidLookupValue(anyString(), anyString())).thenReturn(true);
        when(issueRepository.findByIdentifier(anyString())).thenReturn(Optional.empty());
        when(issueRepository.save(any(Issue.class))).thenAnswer(invocation -> {
            Issue savedIssue = invocation.getArgument(0);
            savedIssue.setId(System.currentTimeMillis()); // Simulate ID assignment
            return savedIssue;
        });

        // Act
        for (int i = 0; i < numberOfThreads; i++) {
            futures.add(executor.submit(() -> {
                try {
                    latch.countDown();
                    latch.await(); // Wait for all threads to be ready
                    
                    Issue issue = createTestIssue();
                    User reporter = createTestUser();
                    issue.setReporter(reporter);
                    
                    return issueService.createIssue(issue);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }));
        }

        // Assert
        List<Issue> results = new ArrayList<>();
        for (Future<Issue> future : futures) {
            Issue result = future.get();
            assertNotNull(result);
            assertNotNull(result.getIdentifier());
            results.add(result);
        }

        assertEquals(numberOfThreads, results.size());
        executor.shutdown();
    }

    private Issue createTestIssue() {
        Issue issue = new Issue();
        issue.setTitle("Test Issue");
        issue.setDescription("Test Description");
        issue.setType("BUG");
        issue.setSeverity("HIGH");
        issue.setPriority("HIGH");
        issue.setStatus("OPEN");
        issue.setEnvironment("DEVELOPMENT");
        return issue;
    }

    private User createTestUser() {
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        return user;
    }
}
