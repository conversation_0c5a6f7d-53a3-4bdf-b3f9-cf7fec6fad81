-- Insert test issues
INSERT INTO bug_tracker.issues (
    identifier,
    title,
    description,
    status,
    priority,
    type,
    severity,
    reporter_id,
    assignee_id,
    created_at,
    updated_at,
    target_date
) VALUES
-- Issue 1: High priority bug
(
    'BUG-001',
    'Login page crashes on mobile devices',
    'When accessing the login page on mobile devices, the application crashes after entering credentials and clicking the login button. This issue is reproducible on both iOS and Android devices.',
    'OPEN',
    'HIGH',
    'BUG',
    'MAJOR',
    1, -- admin user as reporter
    2, -- testuser as assignee
    NOW() - INTERVAL '5 days',
    NOW() - INTERVAL '2 days',
    NOW() + INTERVAL '3 days'
),
-- Issue 2: Medium priority feature
(
    'FEAT-001',
    'Add dark mode support',
    'Implement dark mode support for the entire application to improve user experience in low-light environments. This should include all pages and components.',
    'IN_PROGRESS',
    'MEDIUM',
    'FEATURE',
    'NORMAL',
    2, -- testuser as reporter
    1, -- admin as assignee
    NOW() - INTERVAL '10 days',
    NOW() - INTERVAL '1 day',
    NOW() + INTERVAL '7 days'
),
-- Issue 3: Low priority enhancement
(
    'ENH-001',
    'Improve dashboard loading time',
    'The dashboard takes too long to load when there are many issues. Optimize the queries and implement pagination to improve performance.',
    'OPEN',
    'LOW',
    'ENHANCEMENT',
    'MINOR',
    1, -- admin as reporter
    3, -- testuser2 as assignee
    NOW() - INTERVAL '3 days',
    NOW() - INTERVAL '1 day',
    NOW() + INTERVAL '14 days'
),
-- Issue 4: Critical bug
(
    'BUG-002',
    'Data loss when saving large issues',
    'When saving issues with large descriptions (over 1000 characters), some data is lost and not properly saved to the database.',
    'OPEN',
    'CRITICAL',
    'BUG',
    'CRITICAL',
    3, -- testuser2 as reporter
    1, -- admin as assignee
    NOW() - INTERVAL '1 day',
    NOW(),
    NOW() + INTERVAL '1 day'
),
-- Issue 5: Closed issue
(
    'BUG-003',
    'Fix typo in welcome email',
    'There is a typo in the welcome email sent to new users. The word "account" is misspelled as "acount".',
    'CLOSED',
    'LOW',
    'BUG',
    'TRIVIAL',
    2, -- testuser as reporter
    2, -- testuser as assignee (self-assigned)
    NOW() - INTERVAL '15 days',
    NOW() - INTERVAL '10 days',
    NOW() - INTERVAL '5 days'
);

-- Insert comments for issues
INSERT INTO bug_tracker.comments (
    content,
    issue_id,
    user_id,
    created_at
) VALUES
-- Comments for Issue 1
(
    'I can reproduce this issue on my iPhone 12. It happens after clicking the login button.',
    1,
    2, -- testuser
    NOW() - INTERVAL '4 days'
),
(
    'This seems to be related to the recent UI update. I will investigate further.',
    1,
    1, -- admin
    NOW() - INTERVAL '3 days'
),
-- Comments for Issue 2
(
    'I have started working on this feature. Will update the progress soon.',
    2,
    1, -- admin
    NOW() - INTERVAL '8 days'
),
(
    'I have completed the dark mode for the dashboard and issue list pages. Working on the remaining pages.',
    2,
    1, -- admin
    NOW() - INTERVAL '3 days'
),
-- Comments for Issue 4
(
    'This is a critical issue that needs to be fixed immediately. I am assigning it to the admin.',
    4,
    3, -- testuser2
    NOW() - INTERVAL '1 day'
),
(
    'I am looking into this issue. Will provide an update by end of day.',
    4,
    1, -- admin
    NOW() - INTERVAL '12 hours'
);

-- Add issue watchers
INSERT INTO bug_tracker.issue_watchers (
    issue_id,
    user_id
) VALUES
(1, 3), -- testuser2 watching issue 1
(2, 2), -- testuser watching issue 2
(2, 3), -- testuser2 watching issue 2
(4, 2); -- testuser watching issue 4

-- Add notifications
INSERT INTO bug_tracker.notifications (
    message,
    notification_type,
    user_id,
    issue_id,
    read,
    created_at
) VALUES
(
    'You have been assigned to issue: Login page crashes on mobile devices',
    'ASSIGNMENT',
    2, -- testuser
    1,
    false,
    NOW() - INTERVAL '5 days'
),
(
    'New comment on issue: Login page crashes on mobile devices',
    'COMMENT',
    2, -- testuser
    1,
    true,
    NOW() - INTERVAL '3 days'
),
(
    'You have been assigned to issue: Add dark mode support',
    'ASSIGNMENT',
    1, -- admin
    2,
    true,
    NOW() - INTERVAL '10 days'
),
(
    'Issue status changed to IN_PROGRESS: Add dark mode support',
    'STATUS_CHANGE',
    2, -- testuser
    2,
    false,
    NOW() - INTERVAL '5 days'
),
(
    'You have been assigned to issue: Data loss when saving large issues',
    'ASSIGNMENT',
    1, -- admin
    4,
    false,
    NOW() - INTERVAL '1 day'
);
