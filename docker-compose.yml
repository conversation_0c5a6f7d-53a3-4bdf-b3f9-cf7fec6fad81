version: '3.8'

services:
  postgres:
    image: postgres:14-alpine
    container_name: bugtracker-postgres
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    ports:
      - "${DB_PORT}:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
    networks:
      - bugtracker-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: bugtracker-backend
    depends_on:
      - postgres
    environment:
      SPRING_DATASOURCE_URL: jdbc:postgresql://${DB_HOST}:5432/${DB_NAME}
      SPRING_DATASOURCE_USERNAME: ${DB_USER}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD}
      SERVER_PORT: ${API_PORT}
    ports:
      - "${API_PORT}:${API_PORT}"
    networks:
      - bugtracker-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: bugtracker-frontend
    depends_on:
      - backend
    environment:
      - VITE_API_HOST=${API_HOST}
      - VITE_API_PORT=${API_PORT}
    ports:
      - "${FRONTEND_PORT}:80"
    networks:
      - bugtracker-network

networks:
  bugtracker-network:
    driver: bridge

volumes:
  postgres-data:
