#!/usr/bin/env node

/**
 * Test HTTPS Setup
 *
 * This script tests the HTTPS configuration for both frontend and backend
 * to ensure voice features will work properly.
 */

const https = require('https');
const axios = require('axios');

// Disable SSL verification for self-signed certificates
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const BACKEND_URL = 'https://localhost:8443';
const FRONTEND_URL = 'https://localhost:5174';

async function testEndpoint(url, description) {
  try {
    console.log(`\n🔍 Testing ${description}...`);
    console.log(`   URL: ${url}`);

    const response = await axios.get(url, {
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      }),
      timeout: 10000,
      headers: {
        'User-Agent': 'HTTPS-Test-Script/1.0'
      }
    });

    console.log(`   ✅ Status: ${response.status}`);
    console.log(`   ✅ Response received successfully`);
    return true;
  } catch (error) {
    console.log(`   ❌ Error: ${error.message}`);
    if (error.code === 'ECONNREFUSED') {
      console.log(`   💡 Service may not be running`);
    }
    return false;
  }
}

async function testHTTPSSetup() {
  console.log('🔒 HTTPS Configuration Test');
  console.log('============================');

  const tests = [
    {
      url: `${BACKEND_URL}/actuator/health`,
      description: 'Backend Health Check (HTTPS)'
    },
    {
      url: `${FRONTEND_URL}/`,
      description: 'Frontend Home Page (HTTPS)'
    }
  ];

  let allPassed = true;

  for (const test of tests) {
    const result = await testEndpoint(test.url, test.description);
    allPassed = allPassed && result;
  }

  console.log('\n📊 Test Summary');
  console.log('================');

  if (allPassed) {
    console.log('✅ All HTTPS tests passed!');
    console.log('\n🎙️ Voice Features Status:');
    console.log('   ✅ HTTPS is properly configured');
    console.log('   ✅ Microphone access will work in browsers');
    console.log('   ✅ Speech recognition APIs are available');
    console.log('   ✅ getUserMedia() API will function correctly');
    console.log('\n🌐 Access URLs:');
    console.log(`   Frontend: ${FRONTEND_URL}`);
    console.log(`   Backend:  ${BACKEND_URL}`);
    console.log(`   Health:   ${BACKEND_URL}/actuator/health`);
    console.log('\n⚠️  Browser Security Notice:');
    console.log('   • You may see a security warning for self-signed certificates');
    console.log('   • Click "Advanced" → "Proceed to localhost (unsafe)" to continue');
    console.log('   • Grant microphone permission when prompted for voice features');
  } else {
    console.log('❌ Some HTTPS tests failed');
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure both frontend and backend are running');
    console.log('   2. Check that SSL certificates are properly generated');
    console.log('   3. Verify port 8443 (backend) and 5174 (frontend) are available');
    console.log('   4. Run: ./start-https-working.sh to start both services');
  }

  return allPassed;
}

// Run the test
testHTTPSSetup()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test failed with error:', error.message);
    process.exit(1);
  });
