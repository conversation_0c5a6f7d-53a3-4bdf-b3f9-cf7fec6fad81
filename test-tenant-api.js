#!/usr/bin/env node

/**
 * Test script to verify tenant API functionality with Murthy certificates
 */

const axios = require('axios');

// Disable SSL verification for self-signed certificates
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const API_BASE_URL = 'https://localhost:8443/api';

console.log('🔧 Testing Tenant API with Murthy Certificates');
console.log('===============================================\n');

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000
});

async function testTenantEndpoints() {
  console.log('1️⃣ Testing public tenant endpoints (no tenant ID required)...\n');
  
  try {
    // Test 1: Health check
    console.log('🔍 Testing health endpoint...');
    const healthResponse = await axios.get('https://localhost:8443/actuator/health', {
      timeout: 5000,
      httpsAgent: new (require('https').Agent)({ rejectUnauthorized: false })
    });
    console.log('✅ Health check:', healthResponse.data);
    console.log('');
    
    // Test 2: Tenant registration endpoint (should be public)
    console.log('🔍 Testing tenant registration endpoint...');
    try {
      const registerResponse = await api.get('/tenants/register');
      console.log('✅ Tenant registration endpoint accessible');
    } catch (error) {
      if (error.response?.status === 405) {
        console.log('✅ Tenant registration endpoint accessible (GET not allowed, but endpoint exists)');
      } else {
        console.log('❌ Tenant registration endpoint error:', error.response?.status, error.response?.data);
      }
    }
    console.log('');
    
    // Test 3: Try to validate a tenant ID
    console.log('🔍 Testing tenant validation endpoint...');
    try {
      const validateResponse = await api.post('/tenants/validate-tenant-id', {
        tenantId: 'test-tenant'
      });
      console.log('✅ Tenant validation response:', validateResponse.data);
    } catch (error) {
      console.log('ℹ️  Tenant validation error (expected for non-existent tenant):', error.response?.status, error.response?.data);
    }
    console.log('');
    
  } catch (error) {
    console.log('❌ Public endpoint test failed:', error.message);
    return false;
  }
  
  console.log('2️⃣ Testing tenant-specific endpoints (with tenant ID)...\n');
  
  // First, let's try to create a test tenant
  console.log('🔧 Creating a test tenant...');
  try {
    const tenantData = {
      tenantId: 'test-tenant-' + Date.now(),
      name: 'Test Tenant',
      description: 'Test tenant for API verification',
      subdomain: 'test-' + Date.now(),
      adminEmail: '<EMAIL>',
      adminFirstName: 'Test',
      adminLastName: 'Admin',
      subscriptionPlan: 'BASIC'
    };
    
    const createResponse = await api.post('/tenants/register', tenantData);
    console.log('✅ Test tenant created:', createResponse.data);
    
    const testTenantId = createResponse.data.tenantId;
    console.log(`📋 Using tenant ID: ${testTenantId}\n`);
    
    // Test with tenant header
    console.log('🔍 Testing API with tenant header...');
    api.defaults.headers.common['X-Tenant-ID'] = testTenantId;
    
    try {
      const usersResponse = await api.get('/users');
      console.log('✅ Users endpoint with tenant header:', usersResponse.data);
    } catch (error) {
      console.log('❌ Users endpoint error:', error.response?.status, error.response?.data);
    }
    
    try {
      const issuesResponse = await api.get('/issues');
      console.log('✅ Issues endpoint with tenant header:', issuesResponse.data);
    } catch (error) {
      console.log('❌ Issues endpoint error:', error.response?.status, error.response?.data);
    }
    
  } catch (error) {
    console.log('❌ Tenant creation failed:', error.response?.status, error.response?.data);
    
    // Try with a hardcoded tenant ID that might exist
    console.log('\n🔄 Trying with hardcoded tenant ID...');
    const hardcodedTenantId = 'acme';
    api.defaults.headers.common['X-Tenant-ID'] = hardcodedTenantId;
    
    try {
      const usersResponse = await api.get('/users');
      console.log('✅ Users endpoint with hardcoded tenant:', usersResponse.data);
    } catch (error) {
      console.log('❌ Users endpoint with hardcoded tenant error:', error.response?.status, error.response?.data);
    }
  }
  
  return true;
}

async function testCORSHeaders() {
  console.log('3️⃣ Testing CORS headers...\n');
  
  try {
    const response = await axios.options(API_BASE_URL + '/users', {
      headers: {
        'Origin': 'https://localhost:5173',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'X-Tenant-ID,Content-Type'
      },
      httpsAgent: new (require('https').Agent)({ rejectUnauthorized: false })
    });
    
    console.log('✅ CORS preflight response headers:');
    console.log('   Access-Control-Allow-Origin:', response.headers['access-control-allow-origin']);
    console.log('   Access-Control-Allow-Methods:', response.headers['access-control-allow-methods']);
    console.log('   Access-Control-Allow-Headers:', response.headers['access-control-allow-headers']);
    console.log('   Access-Control-Allow-Credentials:', response.headers['access-control-allow-credentials']);
    
  } catch (error) {
    console.log('❌ CORS test failed:', error.response?.status, error.message);
  }
}

async function runTests() {
  try {
    await testTenantEndpoints();
    await testCORSHeaders();
    
    console.log('\n📊 Test Summary:');
    console.log('================');
    console.log('✅ Backend is running with Murthy certificates');
    console.log('✅ HTTPS connectivity working');
    console.log('ℹ️  Check individual test results above for API functionality');
    console.log('\n🌐 Frontend should be able to connect to:');
    console.log('   Backend: https://localhost:8443');
    console.log('   Frontend: https://localhost:5173');
    console.log('\n💡 If you\'re seeing "Network error" in the frontend:');
    console.log('   1. Check browser console for CORS errors');
    console.log('   2. Ensure tenant ID is being set correctly');
    console.log('   3. Verify certificate acceptance in browser');
    console.log('   4. Check that X-Tenant-ID header is being sent');
    
  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
  }
}

runTests();
