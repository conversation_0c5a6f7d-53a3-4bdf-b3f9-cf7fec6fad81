# API Configuration Guide

This document explains how the API configuration is centralized in the Bug Tracking System.

## Overview

The API configuration is centralized in the following files:

- `.env` - Default environment variables
- `.env.docker` - Docker-specific environment variables
- `.env.local` - Local development environment variables (not committed to version control)
- `frontend/src/config/apiConfig.ts` - TypeScript configuration for the frontend
- `frontend/src/config/dockerConfig.ts` - Docker-specific configuration for the frontend

## Environment Variables

The following environment variables are used:

- `API_HOST` - The hostname of the API server (default: `localhost`)
- `API_PORT` - The port of the API server (default: `8081`)
- `DB_HOST` - The hostname of the database server (default: `postgres`)
- `DB_PORT` - The port of the database server (default: `5432`)
- `DB_NAME` - The name of the database (default: `bugtracker`)
- `DB_USER` - The username for the database (default: `postgres`)
- `DB_PASSWORD` - The password for the database (default: `postgres`)
- `FRONTEND_PORT` - The port for the frontend server (default: `3000`)

## Frontend Configuration

The frontend uses the following configuration files:

- `frontend/src/config/apiConfig.ts` - Contains the API configuration
- `frontend/vite.config.ts` - Uses the API configuration for the development server proxy

### API Configuration

The API configuration is defined in `frontend/src/config/apiConfig.ts`:

```typescript
// Base URL for API
export const API_PORT = import.meta.env.VITE_API_PORT || 8081;
export const API_HOST = import.meta.env.VITE_API_HOST || 'localhost';
export const API_BASE_URL = `http://${API_HOST}:${API_PORT}`;

// For development proxy (used by Vite dev server)
export const DEV_PROXY_TARGET = `http://${API_HOST}:${API_PORT}`;
```

### Vite Configuration

The Vite configuration uses the API configuration for the development server proxy:

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { DEV_PROXY_TARGET } from './src/config/apiConfig'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: DEV_PROXY_TARGET,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, '/api')
      }
    }
  }
})
```

## Docker Configuration

The Docker configuration is defined in `frontend/src/config/dockerConfig.ts`:

```typescript
import { API_PORT, API_HOST } from './apiConfig';

// Docker service names
export const DOCKER_BACKEND_SERVICE = 'backend';

// Docker proxy configuration for nginx
export const DOCKER_PROXY_PASS = `http://${DOCKER_BACKEND_SERVICE}:${API_PORT}/api`;
```

## Docker Compose

The Docker Compose configuration uses environment variables from `.env.docker`:

```yaml
version: '3.8'

services:
  postgres:
    # ...
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
    # ...

  backend:
    # ...
    environment:
      SPRING_DATASOURCE_URL: jdbc:postgresql://${DB_HOST}:5432/${DB_NAME}
      SPRING_DATASOURCE_USERNAME: ${DB_USER}
      SPRING_DATASOURCE_PASSWORD: ${DB_PASSWORD}
      SERVER_PORT: ${API_PORT}
    ports:
      - "${API_PORT}:${API_PORT}"
    # ...

  frontend:
    # ...
    environment:
      - VITE_API_HOST=${API_HOST}
      - VITE_API_PORT=${API_PORT}
    ports:
      - "${FRONTEND_PORT}:80"
    # ...
```

## Starting the Application

### Local Development

1. Start the backend:
   ```
   cd backend
   ./mvnw spring-boot:run
   ```

2. Start the frontend:
   ```
   cd frontend
   npm run dev
   ```

### Docker

1. Start the Docker containers:
   ```
   ./docker-start.sh
   ```

## Changing the API Port

To change the API port:

1. Update the `.env` file:
   ```
   API_PORT=8081
   ```

2. Update the `.env.docker` file:
   ```
   API_PORT=8081
   ```

3. Update the `backend/src/main/resources/application.yml` file:
   ```yaml
   server:
     port: 8081
   ```

4. Restart the application
