#!/bin/bash

# Multitenant Bug Tracking System Startup Script
# This script helps set up and start the multitenant application

set -e

echo "🏢 Starting Multitenant Bug Tracking System Setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if PostgreSQL is running
check_postgres() {
    print_status "Checking PostgreSQL connection..."

    if pg_isready -h localhost -p 5432 >/dev/null 2>&1; then
        print_success "PostgreSQL is running"
    else
        print_error "PostgreSQL is not running. Please start PostgreSQL first."
        exit 1
    fi
}

# Create database if it doesn't exist
setup_database() {
    print_status "Setting up database..."

    # Check if database exists
    if psql -h localhost -U postgres -lqt | cut -d \| -f 1 | grep -qw rnd_app; then
        print_success "Database 'rnd_app' already exists"
    else
        print_status "Creating database 'rnd_app'..."
        createdb -h localhost -U postgres rnd_app
        print_success "Database 'rnd_app' created"
    fi

    # Create public schema and set permissions
    print_status "Setting up database schemas..."
    psql -h localhost -U postgres -d rnd_app -c "
        CREATE SCHEMA IF NOT EXISTS public;
        GRANT ALL ON SCHEMA public TO postgres;
        GRANT ALL ON SCHEMA public TO public;
    " >/dev/null 2>&1

    print_success "Database setup completed"
}

# Build backend
build_backend() {
    print_status "Building backend..."

    cd backend

    # Always set JAVA_HOME to ensure correct version
    export JAVA_HOME="/usr/local/opt/openjdk@21/libexec/openjdk.jdk/Contents/Home"
    print_status "Set JAVA_HOME to $JAVA_HOME for build"

    # Check if Maven is available
    if command -v mvn >/dev/null 2>&1; then
        print_status "Using Maven to build..."
        mvn clean compile -q
        print_success "Backend built successfully"
    else
        print_warning "Maven not found. Please build the backend manually."
    fi

    cd ..
}

# Install frontend dependencies
setup_frontend() {
    print_status "Setting up frontend..."

    cd frontend

    # Check if npm is available
    if command -v npm >/dev/null 2>&1; then
        if [ ! -d "node_modules" ]; then
            print_status "Installing frontend dependencies..."
            npm install
        else
            print_success "Frontend dependencies already installed"
        fi
    else
        print_warning "npm not found. Please install frontend dependencies manually."
    fi

    cd ..
}

# Start backend
start_backend() {
    print_status "Starting backend server..."

    cd backend

    # Always set JAVA_HOME to ensure correct version
    export JAVA_HOME="/usr/local/opt/openjdk@21/libexec/openjdk.jdk/Contents/Home"
    print_status "Set JAVA_HOME to $JAVA_HOME"

    # Start backend in background
    if command -v mvn >/dev/null 2>&1; then
        print_status "Starting Spring Boot application..."
        mvn spring-boot:run > ../logs/backend.log 2>&1 &
        BACKEND_PID=$!
        echo $BACKEND_PID > ../backend.pid
        print_success "Backend started with PID $BACKEND_PID"
    else
        print_warning "Maven not found. Please start the backend manually."
    fi

    cd ..
}

# Start frontend
start_frontend() {
    print_status "Starting frontend server..."

    cd frontend

    if command -v npm >/dev/null 2>&1; then
        print_status "Starting React development server..."
        npm start > ../logs/frontend.log 2>&1 &
        FRONTEND_PID=$!
        echo $FRONTEND_PID > ../frontend.pid
        print_success "Frontend started with PID $FRONTEND_PID"
    else
        print_warning "npm not found. Please start the frontend manually."
    fi

    cd ..
}

# Wait for services to start
wait_for_services() {
    print_status "Waiting for services to start..."

    # Wait for backend
    print_status "Waiting for backend to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:8081/actuator/health >/dev/null 2>&1; then
            print_success "Backend is ready!"
            break
        fi
        if [ $i -eq 30 ]; then
            print_warning "Backend may not be ready yet. Check logs/backend.log"
        fi
        sleep 2
    done

    # Wait for frontend
    print_status "Waiting for frontend to be ready..."
    for i in {1..20}; do
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_success "Frontend is ready!"
            break
        fi
        if [ $i -eq 20 ]; then
            print_warning "Frontend may not be ready yet. Check logs/frontend.log"
        fi
        sleep 3
    done
}

# Create sample tenant
create_sample_tenant() {
    print_status "Creating sample tenant..."

    # Wait a bit more for backend to be fully ready
    sleep 5

    SAMPLE_TENANT='{
        "tenantId": "demo",
        "name": "Demo Corporation",
        "subdomain": "demo",
        "adminEmail": "<EMAIL>",
        "adminFirstName": "Demo",
        "adminLastName": "Admin",
        "adminPassword": "DemoPass123!",
        "description": "Demo tenant for testing multitenant functionality",
        "maxUsers": 50,
        "maxStorageMb": 500,
        "subscriptionPlan": "BASIC"
    }'

    if curl -s -X POST http://localhost:8081/api/tenants/register \
        -H "Content-Type: application/json" \
        -d "$SAMPLE_TENANT" >/dev/null 2>&1; then
        print_success "Sample tenant 'demo' created successfully!"
        print_status "Login credentials:"
        print_status "  Username: demo_admin"
        print_status "  Password: DemoPass123!"
        print_status "  Tenant ID: demo"
    else
        print_warning "Failed to create sample tenant. You can create one manually."
    fi
}

# Display access information
show_access_info() {
    echo ""
    echo "🎉 Multitenant Bug Tracking System is ready!"
    echo ""
    echo "📱 Access URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:8081"
    echo "  API Documentation: http://localhost:8081/swagger-ui.html"
    echo ""
    echo "🏢 Tenant Management:"
    echo "  Register new tenant: http://localhost:3000/tenant-registration"
    echo "  Tenant API: http://localhost:8081/api/tenants"
    echo ""
    echo "🔐 Sample Tenant (demo):"
    echo "  Login URL: http://localhost:3000/login"
    echo "  Username: demo_admin"
    echo "  Password: DemoPass123!"
    echo "  Tenant ID: demo (use X-Tenant-ID header)"
    echo ""
    echo "📋 Testing Commands:"
    echo "  # Register new tenant"
    echo "  curl -X POST http://localhost:8081/api/tenants/register \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -d '{\"tenantId\":\"test\",\"name\":\"Test Corp\",\"subdomain\":\"test\",\"adminEmail\":\"<EMAIL>\",\"adminFirstName\":\"Test\",\"adminLastName\":\"Admin\",\"adminPassword\":\"TestPass123!\"}'"
    echo ""
    echo "  # Login to tenant"
    echo "  curl -X POST http://localhost:8081/api/auth/signin \\"
    echo "    -H 'Content-Type: application/json' \\"
    echo "    -H 'X-Tenant-ID: demo' \\"
    echo "    -d '{\"username\":\"demo_admin\",\"password\":\"DemoPass123!\"}'"
    echo ""
    echo "📁 Log Files:"
    echo "  Backend: logs/backend.log"
    echo "  Frontend: logs/frontend.log"
    echo ""
    echo "🛑 To stop services:"
    echo "  ./stop-multitenant.sh"
    echo ""
}

# Create logs directory
mkdir -p logs

# Main execution
main() {
    echo "🚀 Starting Multitenant Bug Tracking System..."
    echo ""

    check_postgres
    setup_database
    build_backend
    setup_frontend
    start_backend
    start_frontend
    wait_for_services
    create_sample_tenant
    show_access_info
}

# Handle script interruption
cleanup() {
    print_warning "Script interrupted. Cleaning up..."
    if [ -f backend.pid ]; then
        kill $(cat backend.pid) 2>/dev/null || true
        rm backend.pid
    fi
    if [ -f frontend.pid ]; then
        kill $(cat frontend.pid) 2>/dev/null || true
        rm frontend.pid
    fi
    exit 1
}

trap cleanup INT TERM

# Run main function
main

print_success "Setup completed! The system is now running."
print_status "Press Ctrl+C to stop all services."

# Keep script running
while true; do
    sleep 10
done
