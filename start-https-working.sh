#!/bin/bash

# Start Working HTTPS Development Environment
# This script properly configures and starts both services with HTTPS

echo "🔒 Starting Working HTTPS Development Environment"
echo "================================================="

# Function to kill background processes on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo "   Backend stopped"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo "   Frontend stopped"
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Step 1: Configure backend for HTTPS
echo "🔧 Configuring backend for HTTPS..."
cat > backend/src/main/resources/application-https.yml << 'EOF'
server:
  port: 8443
  servlet:
    context-path: /
  ssl:
    enabled: true
    key-store: classpath:certs/murthy.jks
    key-store-password: changeit
    key-store-type: J<PERSON>
    key-alias: murthy
    key-password: changeit
    protocol: TLS
    enabled-protocols: TLSv1.2,TLSv1.3

# Enable CORS for HTTPS
cors:
  allowed-origins:
    - https://localhost:5173
    - https://localhost:5174
    - https://127.0.0.1:5173
    - https://127.0.0.1:5174
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true
EOF

# Step 2: Start Backend with HTTPS profile
echo "🚀 Starting Backend with HTTPS..."
cd backend
export JAVA_HOME="/usr/local/opt/openjdk@21/libexec/openjdk.jdk/Contents/Home"
mvn spring-boot:run -Dspring-boot.run.profiles=https > ../logs/backend-https.log 2>&1 &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 15

# Check if backend is running
echo "🔍 Checking backend health..."
for i in {1..6}; do
    if curl -k -s https://localhost:8443/api/actuator/health > /dev/null 2>&1; then
        echo "✅ Backend health check passed"
        break
    elif [ $i -eq 6 ]; then
        echo "❌ Backend failed to start. Check logs/backend-https.log"
        echo "📋 Last few lines of backend log:"
        tail -10 logs/backend-https.log
        cleanup
        exit 1
    else
        echo "   Attempt $i/6 - waiting 5 more seconds..."
        sleep 5
    fi
done

# Step 3: Configure frontend for HTTPS
echo "🔧 Configuring frontend for HTTPS..."
cat > frontend/vite.config.https.ts << 'EOF'
import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import fs from 'fs'
import path from 'path'

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  const certPath = path.resolve(__dirname, '../certs/murthy.crt')
  const keyPath = path.resolve(__dirname, '../certs/murthy-ca.key')

  return {
    plugins: [react()],
    server: {
      port: 5173,
      host: true,
      https: {
        key: fs.readFileSync(keyPath),
        cert: fs.readFileSync(certPath),
      },
      proxy: {
        '/api': {
          target: 'https://localhost:8443',
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error:', err);
            });
          }
        }
      }
    },
    preview: {
      port: 4173,
      host: true,
      https: {
        key: fs.readFileSync(keyPath),
        cert: fs.readFileSync(certPath),
      },
    }
  }
})
EOF

# Step 4: Start Frontend with HTTPS
echo "🚀 Starting Frontend with HTTPS..."
cd frontend
npx vite --config vite.config.https.ts > ../logs/frontend-https.log 2>&1 &
FRONTEND_PID=$!
cd ..

# Wait for frontend to start
echo "⏳ Waiting for frontend to start..."
sleep 8

echo ""
echo "🎉 HTTPS Development Environment Started!"
echo "========================================"
echo ""
echo "🌐 Frontend: https://localhost:5173"
echo "🔧 Backend:  https://localhost:8443"
echo "📊 Health:   https://localhost:8443/api/actuator/health"
echo ""
echo "🎙️ Voice Features Ready!"
echo "   • Microphone access will work properly over HTTPS"
echo "   • Speech recognition APIs are available"
echo "   • Text-to-speech functionality enabled"
echo ""
echo "⚠️  Browser Security Notice:"
echo "   • You may see a security warning for self-signed certificates"
echo "   • Click 'Advanced' → 'Proceed to localhost (unsafe)' to continue"
echo "   • Grant microphone permission when prompted"
echo ""
echo "🔧 Logs:"
echo "   • Backend:  logs/backend-https.log"
echo "   • Frontend: logs/frontend-https.log"
echo ""
echo "Press Ctrl+C to stop all services"

# Create logs directory if it doesn't exist
mkdir -p logs

# Wait for user to stop services
wait $BACKEND_PID $FRONTEND_PID
