const axios = require('axios');

// Test database connection and API endpoints
async function testDatabaseConnection() {
  console.log('🔍 Testing Database Connection and API Endpoints...\n');

  const baseURL = 'http://localhost:8081/api';
  
  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    try {
      const healthResponse = await axios.get(`${baseURL}/actuator/health`);
      console.log('✅ Health check passed:', healthResponse.data);
    } catch (error) {
      console.log('❌ Health check failed:', error.message);
    }

    // Test 2: Test authentication endpoint
    console.log('\n2. Testing authentication...');
    try {
      const authResponse = await axios.post(`${baseURL}/auth/signin`, {
        username: 'admin',
        password: 'password'
      });
      console.log('✅ Authentication successful');
      console.log('Token received:', authResponse.data.token ? 'Yes' : 'No');
      
      const token = authResponse.data.token;
      
      // Test 3: Test protected endpoint with token
      if (token) {
        console.log('\n3. Testing protected endpoint...');
        try {
          const usersResponse = await axios.get(`${baseURL}/users`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          console.log('✅ Protected endpoint accessible');
          console.log('Users found:', Array.isArray(usersResponse.data) ? usersResponse.data.length : 'Unknown format');
        } catch (error) {
          console.log('❌ Protected endpoint failed:', error.response?.status, error.response?.data);
        }

        // Test 4: Test issues endpoint
        console.log('\n4. Testing issues endpoint...');
        try {
          const issuesResponse = await axios.get(`${baseURL}/issues`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          console.log('✅ Issues endpoint accessible');
          const issues = issuesResponse.data.content || issuesResponse.data;
          console.log('Issues found:', Array.isArray(issues) ? issues.length : 'Unknown format');
        } catch (error) {
          console.log('❌ Issues endpoint failed:', error.response?.status, error.response?.data);
        }

        // Test 5: Test notifications endpoint
        console.log('\n5. Testing notifications endpoint...');
        try {
          const notificationsResponse = await axios.get(`${baseURL}/notifications`, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          console.log('✅ Notifications endpoint accessible');
          const notifications = notificationsResponse.data.content || notificationsResponse.data;
          console.log('Notifications found:', Array.isArray(notifications) ? notifications.length : 'Unknown format');
        } catch (error) {
          console.log('❌ Notifications endpoint failed:', error.response?.status, error.response?.data);
        }
      }
      
    } catch (error) {
      console.log('❌ Authentication failed:', error.response?.status, error.response?.data);
    }

  } catch (error) {
    console.log('❌ General error:', error.message);
  }

  console.log('\n🏁 Database connection test completed!');
  console.log('\n📋 Next Steps:');
  console.log('1. Ensure PostgreSQL is running on localhost:5432');
  console.log('2. Ensure the backend Spring Boot application is running on localhost:8081');
  console.log('3. Check that the database schema "bug_tracker" exists');
  console.log('4. Verify that test data has been inserted');
}

// Run the test
testDatabaseConnection().catch(console.error);
