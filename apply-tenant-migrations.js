const { Client } = require('./backend/node_modules/pg');

// Connection configuration
const client = new Client({
  connectionString: 'postgres://postgres:password@localhost:5432/rnd_app'
});

async function applyTenantMigrations() {
  try {
    // Connect to the database
    await client.connect();
    console.log('Connected to PostgreSQL database');

    // Set the search path to the tenant schema
    await client.query('SET search_path TO tenant_murthy1');
    console.log('Set search path to tenant_murthy1 schema');

    // Add new columns to issues table
    console.log('\n1. Adding new columns to issues table...');
    
    try {
      await client.query('ALTER TABLE issues ADD COLUMN IF NOT EXISTS dev_completion_date TIMESTAMP');
      console.log('✅ Added dev_completion_date column');
    } catch (error) {
      console.log('⚠️ dev_completion_date column may already exist:', error.message);
    }

    try {
      await client.query('ALTER TABLE issues ADD COLUMN IF NOT EXISTS qc_completion_date TIMESTAMP');
      console.log('✅ Added qc_completion_date column');
    } catch (error) {
      console.log('⚠️ qc_completion_date column may already exist:', error.message);
    }

    try {
      await client.query('ALTER TABLE issues ADD COLUMN IF NOT EXISTS root_cause TEXT');
      console.log('✅ Added root_cause column');
    } catch (error) {
      console.log('⚠️ root_cause column may already exist:', error.message);
    }

    // Migrate existing target_date to dev_completion_date
    console.log('\n2. Migrating target_date to dev_completion_date...');
    const migrateResult = await client.query(`
      UPDATE issues 
      SET dev_completion_date = target_date 
      WHERE target_date IS NOT NULL AND dev_completion_date IS NULL
    `);
    console.log(`✅ Migrated ${migrateResult.rowCount} records from target_date to dev_completion_date`);

    // Update ISSUE_SEVERITY lookup values
    console.log('\n3. Updating ISSUE_SEVERITY lookup values...');
    await client.query("DELETE FROM lookup_values WHERE category = 'ISSUE_SEVERITY'");
    
    const severityValues = [
      ['CRITICAL', 'Critical', 'Critical severity issues', 10],
      ['HIGH', 'High', 'High severity issues', 20],
      ['MEDIUM', 'Medium', 'Medium severity issues', 30],
      ['LOW', 'Low', 'Low severity issues', 40]
    ];

    for (const [code, displayName, description, sortOrder] of severityValues) {
      await client.query(`
        INSERT INTO lookup_values (category, code, display_name, description, sort_order, is_active, created_at, updated_at) 
        VALUES ('ISSUE_SEVERITY', $1, $2, $3, $4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [code, displayName, description, sortOrder]);
    }
    console.log('✅ Updated ISSUE_SEVERITY lookup values');

    // Update ISSUE_PRIORITY lookup values
    console.log('\n4. Updating ISSUE_PRIORITY lookup values...');
    await client.query("DELETE FROM lookup_values WHERE category = 'ISSUE_PRIORITY'");
    
    const priorityValues = [
      ['CRITICAL', 'Critical', 'Critical priority issues', 10],
      ['HIGH', 'High', 'High priority issues', 20],
      ['MEDIUM', 'Medium', 'Medium priority issues', 30],
      ['LOW', 'Low', 'Low priority issues', 40]
    ];

    for (const [code, displayName, description, sortOrder] of priorityValues) {
      await client.query(`
        INSERT INTO lookup_values (category, code, display_name, description, sort_order, is_active, created_at, updated_at) 
        VALUES ('ISSUE_PRIORITY', $1, $2, $3, $4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [code, displayName, description, sortOrder]);
    }
    console.log('✅ Updated ISSUE_PRIORITY lookup values');

    // Update ISSUE_STATUS lookup values
    console.log('\n5. Updating ISSUE_STATUS lookup values...');
    await client.query("DELETE FROM lookup_values WHERE category = 'ISSUE_STATUS'");
    
    const statusValues = [
      ['NEW', 'New', 'Newly created issue', 10],
      ['OPEN', 'Open', 'Issue is open and assigned', 20],
      ['IN_PROGRESS', 'In Progress', 'Issue is being worked on', 30],
      ['UNDER_REVIEW', 'Under Review', 'Issue fix is under review', 40],
      ['DEV_COMPLETED', 'Dev Completed', 'Development work is completed', 50],
      ['QC_COMPLETED', 'QC Completed', 'QC testing is completed', 60],
      ['UAT_COMPLETED', 'UAT Completed', 'UAT testing is completed', 70],
      ['HOLD', 'Hold', 'Issue is on hold', 80],
      ['NOT_AN_ISSUE', 'Not an Issue', 'Determined to not be an issue', 90],
      ['REOPENED', 'Reopened', 'Issue has been reopened', 100],
      ['CLOSED', 'Closed', 'Issue has been closed', 110]
    ];

    for (const [code, displayName, description, sortOrder] of statusValues) {
      await client.query(`
        INSERT INTO lookup_values (category, code, display_name, description, sort_order, is_active, created_at, updated_at) 
        VALUES ('ISSUE_STATUS', $1, $2, $3, $4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [code, displayName, description, sortOrder]);
    }
    console.log('✅ Updated ISSUE_STATUS lookup values');

    // Update ISSUE_ENVIRONMENT lookup values
    console.log('\n6. Updating ISSUE_ENVIRONMENT lookup values...');
    await client.query("DELETE FROM lookup_values WHERE category = 'ISSUE_ENVIRONMENT'");
    
    const environmentValues = [
      ['DEVELOPMENT', 'Development', 'Development environment', 10],
      ['QC', 'QC', 'Quality Control environment', 20],
      ['UAT', 'UAT', 'User Acceptance Testing environment', 30],
      ['PRODUCTION', 'Production', 'Production environment', 40]
    ];

    for (const [code, displayName, description, sortOrder] of environmentValues) {
      await client.query(`
        INSERT INTO lookup_values (category, code, display_name, description, sort_order, is_active, created_at, updated_at) 
        VALUES ('ISSUE_ENVIRONMENT', $1, $2, $3, $4, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [code, displayName, description, sortOrder]);
    }
    console.log('✅ Updated ISSUE_ENVIRONMENT lookup values');

    // Update existing issues to have valid statuses
    console.log('\n7. Updating existing issues with valid statuses...');
    const statusUpdateResult = await client.query(`
      UPDATE issues 
      SET status = 'NEW' 
      WHERE status NOT IN ('NEW', 'OPEN', 'IN_PROGRESS', 'UNDER_REVIEW', 'DEV_COMPLETED', 'QC_COMPLETED', 'UAT_COMPLETED', 'HOLD', 'NOT_AN_ISSUE', 'REOPENED', 'CLOSED')
    `);
    console.log(`✅ Updated ${statusUpdateResult.rowCount} issues with invalid statuses`);

    // Create indexes for better performance
    console.log('\n8. Creating indexes...');
    try {
      await client.query('CREATE INDEX IF NOT EXISTS idx_issues_dev_completion_date ON issues(dev_completion_date)');
      console.log('✅ Created index on dev_completion_date');
    } catch (error) {
      console.log('⚠️ Index on dev_completion_date may already exist');
    }

    try {
      await client.query('CREATE INDEX IF NOT EXISTS idx_issues_qc_completion_date ON issues(qc_completion_date)');
      console.log('✅ Created index on qc_completion_date');
    } catch (error) {
      console.log('⚠️ Index on qc_completion_date may already exist');
    }

    console.log('\n✅ All tenant migrations applied successfully!');

  } catch (error) {
    console.error('❌ Error applying tenant migrations:', error);
  } finally {
    // Close the connection
    await client.end();
    console.log('\nConnection closed');
  }
}

// Run the migration
applyTenantMigrations();
