const axios = require('axios');

// Test if the backend is running
async function testBackend() {
  console.log('Testing if the backend is running...');

  try {
    // Test a simple GET request to the root URL
    const response = await axios.get('http://localhost:8081');
    console.log('Backend response:', response.status, response.data);
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('Backend is not running or not accessible');
    } else {
      console.log('Backend error:', error.response?.status, error.response?.data);
    }
  }
}

// Run the test
testBackend();
