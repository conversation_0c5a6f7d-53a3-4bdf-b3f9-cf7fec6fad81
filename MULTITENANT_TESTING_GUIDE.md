# Multitenant System Testing Guide

## Prerequisites

1. **PostgreSQL** running on localhost:5432
2. **Java 21** installed and JAVA_HOME set
3. **<PERSON>ven** for building the backend
4. **Node.js & npm** for the frontend
5. **curl** for API testing

## Quick Start

### 1. Start the System
```bash
./start-multitenant.sh
```

This script will:
- Check PostgreSQL connection
- Set up the database
- Build and start the backend
- Install dependencies and start the frontend
- Create a sample tenant for testing

### 2. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8081
- **API Docs**: http://localhost:8081/swagger-ui.html

## Testing Scenarios

### Scenario 1: Register a New Tenant

#### Using the Frontend
1. Navigate to http://localhost:3000/tenant-registration
2. Fill in the tenant registration form:
   - Tenant ID: `acme`
   - Organization Name: `ACME Corporation`
   - Subdomain: `acme`
   - Admin Email: `<EMAIL>`
   - Admin First Name: `<PERSON>`
   - Admin Last Name: `Doe`
   - Admin Password: `SecurePass123!`
   - Max Users: `100`
   - Max Storage: `1000`
   - Subscription Plan: `BASIC`

#### Using API
```bash
curl -X POST http://localhost:8081/api/tenants/register \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "acme",
    "name": "ACME Corporation",
    "subdomain": "acme",
    "adminEmail": "<EMAIL>",
    "adminFirstName": "John",
    "adminLastName": "Doe",
    "adminPassword": "SecurePass123!",
    "description": "ACME Corp Bug Tracking",
    "maxUsers": 100,
    "maxStorageMb": 1000,
    "subscriptionPlan": "BASIC"
  }'
```

**Expected Response:**
```json
{
  "message": "Tenant registered successfully",
  "tenant": {
    "id": 1,
    "tenantId": "acme",
    "name": "ACME Corporation",
    "subdomain": "acme",
    "status": "ACTIVE"
  },
  "loginUrl": "https://acme.bugtracker.com"
}
```

### Scenario 2: Login to Tenant

#### Using Header-based Tenant Resolution
```bash
curl -X POST http://localhost:8081/api/auth/signin \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: acme" \
  -d '{
    "username": "acme_admin",
    "password": "SecurePass123!"
  }'
```

**Expected Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "type": "Bearer",
  "id": 1,
  "username": "acme_admin",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "roles": ["ROLE_ADMIN"],
  "tenant": {
    "id": "acme",
    "name": "ACME Corporation",
    "subdomain": "acme"
  }
}
```

### Scenario 3: Access Tenant Data

#### Get Issues for Tenant
```bash
# Extract token from login response
TOKEN="your_jwt_token_here"

curl -X GET http://localhost:8081/api/issues \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: acme"
```

#### Create Issue for Tenant
```bash
curl -X POST http://localhost:8081/api/issues \
  -H "Authorization: Bearer $TOKEN" \
  -H "X-Tenant-ID: acme" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Sample Bug Report",
    "description": "This is a test bug for tenant acme",
    "type": "Bug",
    "priority": "High",
    "severity": "Major",
    "environment": "Production"
  }'
```

### Scenario 4: Register User in Tenant

```bash
curl -X POST http://localhost:8081/api/auth/signup \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: acme" \
  -d '{
    "username": "developer1",
    "email": "<EMAIL>",
    "password": "DevPass123!",
    "firstName": "Jane",
    "lastName": "Developer",
    "department": "Engineering"
  }'
```

### Scenario 5: Test Data Isolation

#### Create Two Tenants
```bash
# Create first tenant
curl -X POST http://localhost:8081/api/tenants/register \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "company1",
    "name": "Company One",
    "subdomain": "company1",
    "adminEmail": "<EMAIL>",
    "adminFirstName": "Admin",
    "adminLastName": "One",
    "adminPassword": "AdminPass123!"
  }'

# Create second tenant
curl -X POST http://localhost:8081/api/tenants/register \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "company2",
    "name": "Company Two",
    "subdomain": "company2",
    "adminEmail": "<EMAIL>",
    "adminFirstName": "Admin",
    "adminLastName": "Two",
    "adminPassword": "AdminPass123!"
  }'
```

#### Login to Each Tenant and Create Issues
```bash
# Login to company1
TOKEN1=$(curl -s -X POST http://localhost:8081/api/auth/signin \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: company1" \
  -d '{"username": "company1_admin", "password": "AdminPass123!"}' \
  | jq -r '.token')

# Login to company2
TOKEN2=$(curl -s -X POST http://localhost:8081/api/auth/signin \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: company2" \
  -d '{"username": "company2_admin", "password": "AdminPass123!"}' \
  | jq -r '.token')

# Create issue in company1
curl -X POST http://localhost:8081/api/issues \
  -H "Authorization: Bearer $TOKEN1" \
  -H "X-Tenant-ID: company1" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Company1 Issue",
    "description": "This issue belongs to company1",
    "type": "Bug",
    "priority": "High",
    "severity": "Major",
    "environment": "Production"
  }'

# Create issue in company2
curl -X POST http://localhost:8081/api/issues \
  -H "Authorization: Bearer $TOKEN2" \
  -H "X-Tenant-ID: company2" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Company2 Issue",
    "description": "This issue belongs to company2",
    "type": "Feature",
    "priority": "Medium",
    "severity": "Minor",
    "environment": "Development"
  }'

# Verify company1 can only see their issues
curl -X GET http://localhost:8081/api/issues \
  -H "Authorization: Bearer $TOKEN1" \
  -H "X-Tenant-ID: company1"

# Verify company2 can only see their issues
curl -X GET http://localhost:8081/api/issues \
  -H "Authorization: Bearer $TOKEN2" \
  -H "X-Tenant-ID: company2"
```

### Scenario 6: Test Tenant Management

#### List All Tenants (Admin Only)
```bash
curl -X GET http://localhost:8081/api/tenants
```

#### Get Tenant by Subdomain
```bash
curl -X GET http://localhost:8081/api/tenants/by-subdomain/acme
```

#### Suspend a Tenant
```bash
curl -X POST http://localhost:8081/api/tenants/1/suspend
```

#### Activate a Tenant
```bash
curl -X POST http://localhost:8081/api/tenants/1/activate
```

## Database Verification

### Check Tenant Schemas
```sql
-- Connect to PostgreSQL
psql -h localhost -U postgres -d rnd_app

-- List all schemas
\dn

-- Check tenant management tables
SELECT * FROM public.tenants;

-- Check tenant-specific data
SET search_path TO tenant_acme;
SELECT * FROM users;
SELECT * FROM issues;
```

### Verify Data Isolation
```sql
-- Check company1 data
SET search_path TO tenant_company1;
SELECT title, description FROM issues;

-- Check company2 data
SET search_path TO tenant_company2;
SELECT title, description FROM issues;

-- Verify no cross-tenant data access
```

## Error Testing

### Test Invalid Tenant
```bash
curl -X POST http://localhost:8081/api/auth/signin \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: nonexistent" \
  -d '{
    "username": "test",
    "password": "test"
  }'
```

**Expected Response:** 404 Not Found with "Tenant not found" error

### Test Suspended Tenant
```bash
# First suspend a tenant
curl -X POST http://localhost:8081/api/tenants/1/suspend

# Then try to login
curl -X POST http://localhost:8081/api/auth/signin \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: acme" \
  -d '{
    "username": "acme_admin",
    "password": "SecurePass123!"
  }'
```

**Expected Response:** 403 Forbidden with "Tenant is not active" error

### Test Missing Tenant Context
```bash
curl -X GET http://localhost:8081/api/issues
```

**Expected Response:** 400 Bad Request with "Tenant ID required" error

## Performance Testing

### Load Test Tenant Registration
```bash
# Create multiple tenants quickly
for i in {1..10}; do
  curl -X POST http://localhost:8081/api/tenants/register \
    -H "Content-Type: application/json" \
    -d "{
      \"tenantId\": \"test$i\",
      \"name\": \"Test Company $i\",
      \"subdomain\": \"test$i\",
      \"adminEmail\": \"admin@test$i.com\",
      \"adminFirstName\": \"Admin\",
      \"adminLastName\": \"$i\",
      \"adminPassword\": \"TestPass123!\"
    }" &
done
wait
```

### Concurrent Access Test
```bash
# Test concurrent access to different tenants
for tenant in company1 company2 acme; do
  curl -X GET http://localhost:8081/api/issues \
    -H "X-Tenant-ID: $tenant" &
done
wait
```

## Monitoring and Logs

### Check Application Logs
```bash
# Backend logs
tail -f logs/backend.log

# Frontend logs
tail -f logs/frontend.log
```

### Monitor Database Connections
```sql
-- Check active connections per schema
SELECT schemaname, count(*) 
FROM pg_stat_activity 
WHERE state = 'active' 
GROUP BY schemaname;
```

## Cleanup

### Stop the System
```bash
./stop-multitenant.sh
```

### Reset Database (if needed)
```bash
# Drop and recreate database
dropdb -h localhost -U postgres rnd_app
createdb -h localhost -U postgres rnd_app
```

## Troubleshooting

### Common Issues

1. **PostgreSQL Connection Failed**
   - Ensure PostgreSQL is running: `pg_isready -h localhost -p 5432`
   - Check connection settings in `application.yml`

2. **Backend Won't Start**
   - Check Java version: `java -version`
   - Verify JAVA_HOME: `echo $JAVA_HOME`
   - Check logs: `tail -f logs/backend.log`

3. **Frontend Won't Start**
   - Check Node.js version: `node -v`
   - Install dependencies: `cd frontend && npm install`
   - Check logs: `tail -f logs/frontend.log`

4. **Tenant Not Found Errors**
   - Verify tenant exists: `curl http://localhost:8081/api/tenants/by-tenant-id/your-tenant`
   - Check X-Tenant-ID header is set correctly

5. **Database Schema Issues**
   - Check if tenant schema exists: `\dn` in psql
   - Verify migrations ran: Check `flyway_schema_history` table

This testing guide covers all major aspects of the multitenant system and should help you verify that the implementation is working correctly.
