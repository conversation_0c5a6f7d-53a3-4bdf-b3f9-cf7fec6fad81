#!/bin/bash

# Multitenant Bug Tracking System Stop Script
# This script stops all running services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🛑 Stopping Multitenant Bug Tracking System..."

# Stop backend
if [ -f backend.pid ]; then
    BACKEND_PID=$(cat backend.pid)
    print_status "Stopping backend (PID: $BACKEND_PID)..."
    
    if kill $BACKEND_PID 2>/dev/null; then
        print_success "Backend stopped successfully"
    else
        print_warning "Backend process may have already stopped"
    fi
    
    rm backend.pid
else
    print_status "No backend PID file found"
fi

# Stop frontend
if [ -f frontend.pid ]; then
    FRONTEND_PID=$(cat frontend.pid)
    print_status "Stopping frontend (PID: $FRONTEND_PID)..."
    
    if kill $FRONTEND_PID 2>/dev/null; then
        print_success "Frontend stopped successfully"
    else
        print_warning "Frontend process may have already stopped"
    fi
    
    rm frontend.pid
else
    print_status "No frontend PID file found"
fi

# Kill any remaining processes on the ports
print_status "Checking for remaining processes on ports 8081 and 3000..."

# Kill processes on port 8081 (backend)
BACKEND_PROCESSES=$(lsof -ti:8081 2>/dev/null || true)
if [ ! -z "$BACKEND_PROCESSES" ]; then
    print_status "Killing remaining processes on port 8081..."
    echo $BACKEND_PROCESSES | xargs kill -9 2>/dev/null || true
    print_success "Processes on port 8081 terminated"
fi

# Kill processes on port 3000 (frontend)
FRONTEND_PROCESSES=$(lsof -ti:3000 2>/dev/null || true)
if [ ! -z "$FRONTEND_PROCESSES" ]; then
    print_status "Killing remaining processes on port 3000..."
    echo $FRONTEND_PROCESSES | xargs kill -9 2>/dev/null || true
    print_success "Processes on port 3000 terminated"
fi

# Clean up any Maven/Node processes
print_status "Cleaning up Maven and Node processes..."

# Kill Maven processes
pkill -f "maven" 2>/dev/null || true
pkill -f "spring-boot:run" 2>/dev/null || true

# Kill Node processes related to our project
pkill -f "react-scripts" 2>/dev/null || true

print_success "All services stopped successfully!"

echo ""
echo "📊 System Status:"
echo "  Backend (port 8081): Stopped"
echo "  Frontend (port 3000): Stopped"
echo ""
echo "🔄 To restart the system:"
echo "  ./start-multitenant.sh"
echo ""
