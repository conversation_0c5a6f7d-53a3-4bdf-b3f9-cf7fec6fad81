import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Chip,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchIssues, updateIssueStatus } from '../store/slices/issueSlice';

const IssueBoard: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { issues, loading } = useAppSelector((state) => state.issues);

  const [columns, setColumns] = useState<any>({
    'OPEN': {
      name: 'Open',
      items: []
    },
    'IN_PROGRESS': {
      name: 'In Progress',
      items: []
    },
    'UNDER_REVIEW': {
      name: 'Under Review',
      items: []
    },
    'RESOLVED': {
      name: 'Resolved',
      items: []
    },
    'CLOSED': {
      name: 'Closed',
      items: []
    },
    'REOPENED': {
      name: 'Reopened',
      items: []
    }
  });

  useEffect(() => {
    dispatch(fetchIssues({}));
  }, [dispatch]);

  useEffect(() => {
    if (issues.length > 0) {
      const newColumns = { ...columns };

      // Reset all columns
      Object.keys(newColumns).forEach(key => {
        newColumns[key].items = [];
      });

      // Populate columns with issues
      issues.forEach(issue => {
        if (newColumns[issue.status]) {
          newColumns[issue.status].items.push(issue);
        }
      });

      setColumns(newColumns);
    }
  }, [issues]);

  const handleCreateIssue = () => {
    navigate('/issues/create');
  };

  const onDragEnd = (result: any) => {
    if (!result.destination) return;

    const { source, destination, draggableId } = result;

    if (source.droppableId === destination.droppableId) {
      // Reordering within the same column
      const column = columns[source.droppableId];
      const copiedItems = [...column.items];
      const [removed] = copiedItems.splice(source.index, 1);
      copiedItems.splice(destination.index, 0, removed);

      setColumns({
        ...columns,
        [source.droppableId]: {
          ...column,
          items: copiedItems
        }
      });
    } else {
      // Moving from one column to another
      const sourceColumn = columns[source.droppableId];
      const destColumn = columns[destination.droppableId];
      const sourceItems = [...sourceColumn.items];
      const destItems = [...destColumn.items];
      const [removed] = sourceItems.splice(source.index, 1);

      // Update the issue status in the backend
      const issueId = parseInt(draggableId);
      dispatch(updateIssueStatus({ id: issueId, status: destination.droppableId }));

      // Update the issue status locally
      removed.status = destination.droppableId;

      destItems.splice(destination.index, 0, removed);

      setColumns({
        ...columns,
        [source.droppableId]: {
          ...sourceColumn,
          items: sourceItems
        },
        [destination.droppableId]: {
          ...destColumn,
          items: destItems
        }
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'error';
      case 'HIGH':
        return 'warning';
      case 'MEDIUM':
        return 'info';
      case 'LOW':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Box>
      <Grid container spacing={2} alignItems="center" sx={{ mb: 3 }}>
        <Grid item xs>
          <Typography variant="h4">Issue Board</Typography>
        </Grid>
        <Grid item>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateIssue}
          >
            Create Issue
          </Button>
        </Grid>
      </Grid>

      {loading ? (
        <Typography>Loading issues...</Typography>
      ) : (
        <DragDropContext onDragEnd={onDragEnd}>
          <Grid container spacing={2}>
            {Object.entries(columns).map(([columnId, column]: [string, any]) => (
              <Grid item xs={12} md={2} key={columnId}>
                <Paper sx={{ p: 2, height: '100%' }}>
                  <Typography variant="h6" gutterBottom>
                    {column.name} ({column.items.length})
                  </Typography>
                  <Droppable droppableId={columnId}>
                    {(provided) => (
                      <Box
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        sx={{ minHeight: 500 }}
                      >
                        {column.items.map((item: any, index: number) => (
                          <Draggable key={item.id} draggableId={item.id.toString()} index={index}>
                            {(provided) => (
                              <Card
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                sx={{ mb: 2 }}
                              >
                                <CardContent>
                                  <Typography variant="subtitle1" gutterBottom>
                                    {item.identifier}
                                  </Typography>
                                  <Typography variant="body2" noWrap>
                                    {item.title}
                                  </Typography>
                                  <Box sx={{ mt: 1 }}>
                                    <Chip
                                      label={item.type.replace('_', ' ')}
                                      size="small"
                                      sx={{ mr: 1 }}
                                    />
                                    <Chip
                                      label={item.priority}
                                      color={getPriorityColor(item.priority)}
                                      size="small"
                                    />
                                  </Box>
                                </CardContent>
                                <CardActions>
                                  <Tooltip title="View/Edit">
                                    <IconButton size="small" onClick={() => navigate(`/issues/${item.id}`)}>
                                      <VisibilityIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                </CardActions>
                              </Card>
                            )}
                          </Draggable>
                        ))}
                        {provided.placeholder}
                      </Box>
                    )}
                  </Droppable>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </DragDropContext>
      )}
    </Box>
  );
};

export default IssueBoard;
