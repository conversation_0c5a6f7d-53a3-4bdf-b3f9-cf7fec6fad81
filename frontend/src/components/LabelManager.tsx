import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Autocomplete,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Close as CloseIcon,
  Label as LabelIcon
} from '@mui/icons-material';
import labelService, { Label } from '../services/labelService';

interface LabelManagerProps {
  issueId: number;
  currentLabels: Label[];
  onLabelsChange: (labels: Label[]) => void;
}

const LabelManager: React.FC<LabelManagerProps> = ({
  issueId,
  currentLabels,
  onLabelsChange
}) => {
  const [open, setOpen] = useState(false);
  const [allLabels, setAllLabels] = useState<Label[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedLabels, setSelectedLabels] = useState<Label[]>(currentLabels);

  useEffect(() => {
    setSelectedLabels(currentLabels);
  }, [currentLabels]);

  const fetchAllLabels = async () => {
    try {
      setLoading(true);
      const labels = await labelService.getAllLabels();
      setAllLabels(labels);
    } catch (error: any) {
      console.error('Error fetching labels:', error);
      setError('Failed to fetch labels');
    } finally {
      setLoading(false);
    }
  };

  const handleOpen = () => {
    setOpen(true);
    fetchAllLabels();
  };

  const handleClose = () => {
    setOpen(false);
    setError(null);
  };

  const handleLabelToggle = async (label: Label) => {
    try {
      const isCurrentlySelected = selectedLabels.some(l => l.id === label.id);
      
      if (isCurrentlySelected) {
        // Remove label
        await labelService.removeLabelFromIssue(issueId, label.id);
        const newLabels = selectedLabels.filter(l => l.id !== label.id);
        setSelectedLabels(newLabels);
        onLabelsChange(newLabels);
      } else {
        // Add label
        await labelService.addLabelToIssue(issueId, label.id);
        const newLabels = [...selectedLabels, label];
        setSelectedLabels(newLabels);
        onLabelsChange(newLabels);
      }
    } catch (error: any) {
      console.error('Error toggling label:', error);
      setError('Failed to update label');
    }
  };

  const handleSave = () => {
    handleClose();
  };

  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
        {currentLabels.map((label) => (
          <Chip
            key={label.id}
            label={label.name}
            size="small"
            sx={{
              backgroundColor: label.color,
              color: '#fff',
              fontWeight: 'medium',
              '& .MuiChip-deleteIcon': {
                color: '#fff'
              }
            }}
            onDelete={() => handleLabelToggle(label)}
          />
        ))}
        
        <Tooltip title="Manage Labels">
          <IconButton
            size="small"
            onClick={handleOpen}
            sx={{
              bgcolor: 'rgba(0,0,0,0.04)',
              '&:hover': { bgcolor: 'rgba(0,0,0,0.08)' }
            }}
          >
            <LabelIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Manage Labels</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ mt: 1 }}>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Click labels to add or remove them:
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                {allLabels.map((label) => {
                  const isSelected = selectedLabels.some(l => l.id === label.id);
                  return (
                    <Chip
                      key={label.id}
                      label={label.name}
                      clickable
                      variant={isSelected ? 'filled' : 'outlined'}
                      onClick={() => handleLabelToggle(label)}
                      sx={{
                        backgroundColor: isSelected ? label.color : 'transparent',
                        borderColor: label.color,
                        color: isSelected ? '#fff' : label.color,
                        fontWeight: 'medium',
                        '&:hover': {
                          backgroundColor: isSelected ? label.color : `${label.color}20`
                        }
                      }}
                    />
                  );
                })}
              </Box>
              
              {allLabels.length === 0 && !loading && (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
                  No labels available. Contact an administrator to create labels.
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default LabelManager;
