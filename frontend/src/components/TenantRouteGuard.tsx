import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { CircularProgress, Box, Alert } from '@mui/material';
import tenantAuthService from '../services/tenantAuthService';
import tenantService from '../services/tenantService';
import unifiedAuthManager from '../services/unifiedAuthManager';
import { ROUTES } from '../config/routes';

interface TenantRouteGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireRoles?: string[];
  requireTenant?: boolean;
}

const TenantRouteGuard: React.FC<TenantRouteGuardProps> = ({
  children,
  requireAuth = true,
  requireRoles = [],
  requireTenant = true
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [hasRequiredRoles, setHasRequiredRoles] = useState(false);
  const [tenantContext, setTenantContext] = useState<string | null>(null);
  const location = useLocation();

  useEffect(() => {
    const initializeAuth = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fast authentication check first - no API calls
        if (requireAuth) {
          console.log('TenantRouteGuard: Quick authentication check...');

          // Quick check without API calls
          const hasAuthData = localStorage.getItem('auth_token') || localStorage.getItem('currentTenantId');
          const isQuickAuthenticated = unifiedAuthManager.isAuthenticated();

          if (!hasAuthData || !isQuickAuthenticated) {
            console.log('TenantRouteGuard: No auth data found, redirecting immediately');
            setIsAuthenticated(false);
            setLoading(false);
            return;
          }

          console.log('TenantRouteGuard: Auth data found, proceeding with full check...');
          setIsAuthenticated(true);

          // Check roles if required (quick check)
          if (requireRoles.length > 0) {
            const userRoles = tenantAuthService.getUserRoles();
            const hasRoles = requireRoles.some(role => userRoles.includes(role));
            setHasRequiredRoles(hasRoles);

            if (!hasRoles) {
              setError('You do not have permission to access this page.');
              setLoading(false);
              return;
            }
          } else {
            setHasRequiredRoles(true);
          }
        } else {
          setIsAuthenticated(true);
          setHasRequiredRoles(true);
        }

        // Initialize tenant context only if authenticated (or not required)
        if (requireTenant) {
          const storedTenantId = localStorage.getItem('currentTenantId');

          if (storedTenantId) {
            // Use stored tenant ID for fast access
            console.log('TenantRouteGuard: Using stored tenant ID:', storedTenantId);
            setTenantContext(storedTenantId);
            tenantService.setTenantContext(storedTenantId);
          } else {
            // Try to initialize tenant context (may involve API calls)
            console.log('TenantRouteGuard: Initializing tenant context...');
            const tenantId = await tenantService.initializeTenantContext();

            if (!tenantId) {
              console.log('TenantRouteGuard: No tenant context available');
              setError('No tenant context available. Please access through a tenant-specific URL.');
              setLoading(false);
              return;
            }
            setTenantContext(tenantId);
          }
        }

      } catch (err: any) {
        console.error('Route guard initialization error:', err);
        setError(err.message || 'Failed to initialize authentication');
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, [requireAuth, requireRoles, requireTenant, location.pathname]);

  // Show loading spinner while initializing
  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="200px"
      >
        <CircularProgress />
      </Box>
    );
  }

  // Show error if there's an issue
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {error}
        </Alert>
      </Box>
    );
  }

  // Redirect to welcome page if authentication is required but user is not authenticated
  // This check must come BEFORE tenant context check
  if (requireAuth && !isAuthenticated) {
    console.log('TenantRouteGuard: User not authenticated, redirecting to welcome page');
    return <Navigate to={ROUTES.PUBLIC.WELCOME} state={{ from: location }} replace />;
  }

  // Redirect to tenant selection if no tenant context (only for authenticated users)
  if (requireTenant && !tenantContext) {
    console.log('TenantRouteGuard: Authenticated user but no tenant context, redirecting to tenant selection');
    return <Navigate to={ROUTES.PUBLIC.TENANT_SELECTION} state={{ from: location }} replace />;
  }

  // Show access denied if user doesn't have required roles
  if (requireAuth && !hasRequiredRoles) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Access denied. You do not have the required permissions to view this page.
        </Alert>
      </Box>
    );
  }

  // All checks passed, render the protected content
  return <>{children}</>;
};

export default TenantRouteGuard;
