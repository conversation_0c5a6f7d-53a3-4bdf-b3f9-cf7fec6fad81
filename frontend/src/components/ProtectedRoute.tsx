import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAppSelector } from '../store/hooks';
import unifiedAuthManager from '../services/unifiedAuthManager';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  console.log('ProtectedRoute: Checking authentication using unified auth manager...');

  // Check if user is authenticated using unified auth manager
  const isAuthenticated = unifiedAuthManager.isAuthenticated();
  console.log('ProtectedRoute: Authentication status:', isAuthenticated);

  if (!isAuthenticated) {
    console.log('ProtectedRoute: User not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  console.log('ProtectedRoute: User authenticated, rendering protected content');
  return <>{children}</>;
};

export default ProtectedRoute;
