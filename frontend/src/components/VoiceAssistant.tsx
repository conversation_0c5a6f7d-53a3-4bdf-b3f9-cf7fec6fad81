import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  LinearProgress,
  Chip,
  IconButton,
  Fade,
  Alert,
  Divider
} from '@mui/material';
import {
  Mic as MicIcon,
  <PERSON>c<PERSON>ff as <PERSON>cOffIcon,
  VolumeUp as SpeakIcon,
  Replay as ReplayIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { useVoiceRecording } from '../hooks/useVoiceRecording';
import speechToTextService, { SpeechToTextResult } from '../services/speechToTextService';

export interface ConversationStep {
  id: string;
  question: string;
  response?: string;
  confidence?: number;
  completed: boolean;
  required: boolean;
}

export interface VoiceAssistantData {
  issueDescription: string;
  environment?: string;
  severity?: string;
  priority?: string;
  additionalContext?: string;
}

interface VoiceAssistantProps {
  onComplete: (data: VoiceAssistantData) => void;
  onCancel: () => void;
  isOpen: boolean;
}

const VoiceAssistant: React.FC<VoiceAssistantProps> = ({ onComplete, onCancel, isOpen }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [conversationData, setConversationData] = useState<VoiceAssistantData>({
    issueDescription: ''
  });
  const [error, setError] = useState<string | null>(null);
  const [showTranscript, setShowTranscript] = useState(false);
  const [lastTranscript, setLastTranscript] = useState('');

  const stopRecognitionRef = useRef<(() => void) | null>(null);
  const speechSynthesisRef = useRef<SpeechSynthesisUtterance | null>(null);

  // Conversation flow definition
  const conversationSteps: ConversationStep[] = [
    {
      id: 'issue_description',
      question: "Hello! I'm here to help you create an issue report. Please describe the problem you're experiencing in detail.",
      completed: false,
      required: true
    },
    {
      id: 'environment',
      question: "What environment did this issue occur in? For example, production, staging, or development?",
      completed: false,
      required: false
    },
    {
      id: 'severity',
      question: "How critical is this issue? Is it blocking users, causing minor inconvenience, or somewhere in between?",
      completed: false,
      required: false
    },
    {
      id: 'priority',
      question: "When do you need this resolved? Is it urgent, high priority, normal, or low priority?",
      completed: false,
      required: false
    },
    {
      id: 'additional_context',
      question: "Is there any additional context or information that would help resolve this issue?",
      completed: false,
      required: false
    }
  ];

  const [steps, setSteps] = useState<ConversationStep[]>(conversationSteps);

  // Voice recording hook
  const { state: recordingState, controls: recordingControls } = useVoiceRecording({
    onTranscription: handleTranscription,
    onError: (error) => setError(error),
    maxDuration: 60 // 1 minute per response
  });

  // Handle transcription results
  function handleTranscription(transcript: string, confidence: number) {
    setLastTranscript(transcript);
    setShowTranscript(true);
    
    // Process the transcript based on current step
    processStepResponse(currentStep, transcript, confidence);
  }

  // Process response for current conversation step
  const processStepResponse = useCallback((stepIndex: number, transcript: string, confidence: number) => {
    const step = steps[stepIndex];
    if (!step) return;

    // Update step with response
    const updatedSteps = [...steps];
    updatedSteps[stepIndex] = {
      ...step,
      response: transcript,
      confidence,
      completed: true
    };
    setSteps(updatedSteps);

    // Update conversation data based on step
    const newData = { ...conversationData };
    switch (step.id) {
      case 'issue_description':
        newData.issueDescription = transcript;
        break;
      case 'environment':
        newData.environment = extractEnvironment(transcript);
        break;
      case 'severity':
        newData.severity = extractSeverity(transcript);
        break;
      case 'priority':
        newData.priority = extractPriority(transcript);
        break;
      case 'additional_context':
        newData.additionalContext = transcript;
        break;
    }
    setConversationData(newData);

    // Move to next step or complete
    setTimeout(() => {
      if (stepIndex < steps.length - 1) {
        setCurrentStep(stepIndex + 1);
        speakQuestion(updatedSteps[stepIndex + 1].question);
      } else {
        handleConversationComplete(newData);
      }
    }, 1000);
  }, [steps, conversationData]);

  // Extract environment from natural language
  const extractEnvironment = (text: string): string => {
    const lowerText = text.toLowerCase();
    if (lowerText.includes('production') || lowerText.includes('prod') || lowerText.includes('live')) {
      return 'PRODUCTION';
    }
    if (lowerText.includes('staging') || lowerText.includes('stage')) {
      return 'STAGING';
    }
    if (lowerText.includes('development') || lowerText.includes('dev') || lowerText.includes('local')) {
      return 'DEVELOPMENT';
    }
    if (lowerText.includes('test') || lowerText.includes('testing')) {
      return 'TESTING';
    }
    return 'PRODUCTION'; // Default
  };

  // Extract severity from natural language
  const extractSeverity = (text: string): string => {
    const lowerText = text.toLowerCase();
    if (lowerText.includes('critical') || lowerText.includes('blocking') || lowerText.includes('urgent') || lowerText.includes('severe')) {
      return 'CRITICAL';
    }
    if (lowerText.includes('major') || lowerText.includes('important') || lowerText.includes('significant')) {
      return 'MAJOR';
    }
    if (lowerText.includes('minor') || lowerText.includes('small') || lowerText.includes('low')) {
      return 'MINOR';
    }
    if (lowerText.includes('trivial') || lowerText.includes('cosmetic')) {
      return 'TRIVIAL';
    }
    return 'NORMAL'; // Default
  };

  // Extract priority from natural language
  const extractPriority = (text: string): string => {
    const lowerText = text.toLowerCase();
    if (lowerText.includes('urgent') || lowerText.includes('asap') || lowerText.includes('immediately')) {
      return 'URGENT';
    }
    if (lowerText.includes('high') || lowerText.includes('soon') || lowerText.includes('important')) {
      return 'HIGH';
    }
    if (lowerText.includes('low') || lowerText.includes('later') || lowerText.includes('eventually')) {
      return 'LOW';
    }
    return 'NORMAL'; // Default
  };

  // Text-to-speech functionality
  const speakText = useCallback((text: string) => {
    if ('speechSynthesis' in window) {
      // Cancel any ongoing speech
      window.speechSynthesis.cancel();
      
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.9;
      utterance.pitch = 1;
      utterance.volume = 0.8;
      
      utterance.onstart = () => setIsSpeaking(true);
      utterance.onend = () => setIsSpeaking(false);
      utterance.onerror = () => setIsSpeaking(false);
      
      speechSynthesisRef.current = utterance;
      window.speechSynthesis.speak(utterance);
    }
  }, []);

  // Speak current question
  const speakQuestion = useCallback((question: string) => {
    speakText(question);
  }, [speakText]);

  // Start listening for user response
  const startListening = useCallback(async () => {
    try {
      setError(null);
      setShowTranscript(false);
      
      if (recordingControls.isSupported) {
        // Use real-time speech recognition
        const stopFn = await speechToTextService.startRealTimeRecognition(
          (result: SpeechToTextResult) => {
            handleTranscription(result.transcript, result.confidence);
            setIsListening(false);
            if (stopRecognitionRef.current) {
              stopRecognitionRef.current();
              stopRecognitionRef.current = null;
            }
          },
          (error: string) => {
            setError(error);
            setIsListening(false);
            stopRecognitionRef.current = null;
          },
          { continuous: false, interimResults: false }
        );
        
        stopRecognitionRef.current = stopFn;
        setIsListening(true);
      } else {
        // Fallback to recording
        await recordingControls.startRecording();
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to start listening');
    }
  }, [recordingControls, handleTranscription]);

  // Stop listening
  const stopListening = useCallback(() => {
    if (stopRecognitionRef.current) {
      stopRecognitionRef.current();
      stopRecognitionRef.current = null;
    }
    recordingControls.cancelRecording();
    setIsListening(false);
  }, [recordingControls]);

  // Handle conversation completion
  const handleConversationComplete = useCallback((data: VoiceAssistantData) => {
    speakText("Thank you! I've collected all the information. Let me process this and create your issue report.");
    setTimeout(() => {
      onComplete(data);
    }, 2000);
  }, [onComplete, speakText]);

  // Start conversation when component opens
  useEffect(() => {
    if (isOpen && currentStep === 0) {
      setTimeout(() => {
        speakQuestion(steps[0].question);
      }, 500);
    }
  }, [isOpen, currentStep, speakQuestion, steps]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (speechSynthesisRef.current) {
        window.speechSynthesis.cancel();
      }
      if (stopRecognitionRef.current) {
        stopRecognitionRef.current();
      }
    };
  }, []);

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <Box sx={{ p: 3, maxWidth: 600, mx: 'auto' }}>
      {/* Progress indicator */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Voice Assistant ({currentStep + 1}/{steps.length})
        </Typography>
        <LinearProgress variant="determinate" value={progress} sx={{ mb: 1 }} />
        <Typography variant="caption" color="text.secondary">
          Step {currentStep + 1}: {currentStepData?.id.replace('_', ' ').toUpperCase()}
        </Typography>
      </Box>

      {/* Current question */}
      <Paper elevation={2} sx={{ p: 3, mb: 3, bgcolor: 'primary.50' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <SpeakIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle1" color="primary">
            Assistant
          </Typography>
          {isSpeaking && (
            <Chip label="Speaking..." size="small" sx={{ ml: 2 }} />
          )}
        </Box>
        <Typography variant="body1">
          {currentStepData?.question}
        </Typography>
      </Paper>

      {/* Voice controls */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
        <IconButton
          size="large"
          color={isListening ? "secondary" : "primary"}
          onClick={isListening ? stopListening : startListening}
          disabled={isSpeaking}
          sx={{
            width: 80,
            height: 80,
            bgcolor: isListening ? 'secondary.main' : 'primary.main',
            color: 'white',
            '&:hover': {
              bgcolor: isListening ? 'secondary.dark' : 'primary.dark',
            },
            animation: isListening ? 'pulse 1.5s infinite' : 'none',
            '@keyframes pulse': {
              '0%': { transform: 'scale(1)' },
              '50%': { transform: 'scale(1.1)' },
              '100%': { transform: 'scale(1)' }
            }
          }}
        >
          {isListening ? <MicIcon fontSize="large" /> : <MicOffIcon fontSize="large" />}
        </IconButton>
      </Box>

      {/* Recording state */}
      {isListening && (
        <Box sx={{ textAlign: 'center', mb: 2 }}>
          <Typography variant="body2" color="secondary">
            Listening... Speak now
          </Typography>
          {recordingState.audioLevel > 0 && (
            <LinearProgress 
              variant="determinate" 
              value={recordingState.audioLevel * 100} 
              sx={{ mt: 1, height: 8, borderRadius: 4 }}
              color="secondary"
            />
          )}
        </Box>
      )}

      {/* Last transcript */}
      <Fade in={showTranscript}>
        <Paper elevation={1} sx={{ p: 2, mb: 3, bgcolor: 'success.50' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <CheckIcon color="success" sx={{ mr: 1 }} />
            <Typography variant="subtitle2" color="success.main">
              I heard:
            </Typography>
          </Box>
          <Typography variant="body2">
            "{lastTranscript}"
          </Typography>
        </Paper>
      </Fade>

      {/* Error display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Conversation summary */}
      <Paper elevation={1} sx={{ p: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Conversation Summary:
        </Typography>
        {steps.map((step, index) => (
          <Box key={step.id} sx={{ mb: 1 }}>
            <Typography variant="caption" color="text.secondary">
              {step.id.replace('_', ' ').toUpperCase()}:
            </Typography>
            <Typography variant="body2">
              {step.response || (index === currentStep ? 'Current question...' : 'Pending...')}
            </Typography>
            {index < steps.length - 1 && <Divider sx={{ my: 1 }} />}
          </Box>
        ))}
      </Paper>

      {/* Action buttons */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
        <Button onClick={onCancel} variant="outlined">
          Cancel
        </Button>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton 
            onClick={() => speakQuestion(currentStepData?.question || '')}
            disabled={isSpeaking}
            title="Repeat question"
          >
            <ReplayIcon />
          </IconButton>
          {currentStep > 0 && (
            <Button 
              onClick={() => setCurrentStep(currentStep - 1)}
              variant="outlined"
              size="small"
            >
              Previous
            </Button>
          )}
          {currentStepData?.completed && currentStep < steps.length - 1 && (
            <Button 
              onClick={() => setCurrentStep(currentStep + 1)}
              variant="contained"
              size="small"
            >
              Next
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default VoiceAssistant;
