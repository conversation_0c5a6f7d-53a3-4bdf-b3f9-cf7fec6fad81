import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { CircularProgress, Box } from '@mui/material';
import unifiedAuthManager from '../services/unifiedAuthManager';
import tenantService from '../services/tenantService';
import { ROUTES } from '../config/routes';

/**
 * RootRedirect Component
 *
 * Handles intelligent routing for the root URL (/) based on user authentication
 * and tenant context. This component ensures users are directed to the appropriate
 * page based on their current state:
 *
 * 1. Unauthenticated users -> Welcome page
 * 2. Authenticated users with tenant context -> Dashboard
 * 3. Authenticated users without tenant context -> Tenant selection
 *
 * This is a production-grade solution that prevents routing conflicts and
 * ensures consistent behavior across all scenarios.
 */
const RootRedirect: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [redirectTo, setRedirectTo] = useState<string | null>(null);

  useEffect(() => {
    const determineRedirect = async () => {
      try {
        console.log('RootRedirect: Determining appropriate redirect...');

        // Add a small delay to ensure all auth services are initialized
        await new Promise(resolve => setTimeout(resolve, 100));

        // Check authentication status first
        const isAuthenticated = unifiedAuthManager.isAuthenticated();
        console.log('RootRedirect: Authentication status:', isAuthenticated);

        if (!isAuthenticated) {
          // Clear any stale tenant data for unauthenticated users
          localStorage.removeItem('currentTenantId');
          localStorage.removeItem('tenantInfo');

          // Unauthenticated users go to welcome page
          console.log('RootRedirect: User not authenticated, redirecting to welcome');
          setRedirectTo(ROUTES.PUBLIC.WELCOME);
          return;
        }

        // For authenticated users, check tenant context
        const currentTenantId = localStorage.getItem('currentTenantId');
        console.log('RootRedirect: Current tenant ID from storage:', currentTenantId);

        if (currentTenantId) {
          // Validate that the tenant ID is still valid
          try {
            tenantService.setTenantContext(currentTenantId);
            console.log('RootRedirect: User has valid tenant context, redirecting to dashboard');
            setRedirectTo(ROUTES.APP.DASHBOARD);
            return;
          } catch (tenantError) {
            console.warn('RootRedirect: Stored tenant ID is invalid, clearing and retrying');
            localStorage.removeItem('currentTenantId');
            localStorage.removeItem('tenantInfo');
          }
        }

        // Try to initialize tenant context for authenticated user
        console.log('RootRedirect: Attempting to initialize tenant context...');
        const tenantId = await tenantService.initializeTenantContext();

        if (tenantId) {
          console.log('RootRedirect: Tenant context initialized, redirecting to dashboard');
          setRedirectTo(ROUTES.APP.DASHBOARD);
        } else {
          console.log('RootRedirect: No tenant context available, redirecting to tenant selection');
          setRedirectTo(ROUTES.PUBLIC.TENANT_SELECTION);
        }

      } catch (error) {
        console.error('RootRedirect: Error determining redirect:', error);
        // On error, default to welcome page for safety
        setRedirectTo(ROUTES.PUBLIC.WELCOME);
      } finally {
        setLoading(false);
      }
    };

    determineRedirect();
  }, []);

  // Show loading spinner while determining redirect
  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        }}
      >
        <Box textAlign="center">
          <CircularProgress size={60} sx={{ color: 'white', mb: 2 }} />
          <Box sx={{ color: 'white', fontSize: '1.1rem' }}>
            Loading...
          </Box>
        </Box>
      </Box>
    );
  }

  // Redirect to determined route
  if (redirectTo) {
    console.log('RootRedirect: Redirecting to:', redirectTo);
    return <Navigate to={redirectTo} replace />;
  }

  // Fallback to welcome page if no redirect determined
  console.log('RootRedirect: No redirect determined, defaulting to welcome');
  return <Navigate to={ROUTES.PUBLIC.WELCOME} replace />;
};

export default RootRedirect;
