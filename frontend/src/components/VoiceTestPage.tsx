import React, { useState } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Button,
  Paper,
  Alert,
  Divider,
  Card,
  CardContent,
  CardActions,
  Grid
} from '@mui/material';
import {
  Mic as MicIcon,
  SmartToy as AIIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import { useVoiceRecording } from '../hooks/useVoiceRecording';
import speechToTextService from '../services/speechToTextService';
import VoiceAssistant, { VoiceAssistantData } from './VoiceAssistant';
import AIAssistDialog from './AIAssistDialog';

const VoiceTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [showVoiceAssistant, setShowVoiceAssistant] = useState(false);
  const [showAIDialog, setShowAIDialog] = useState(false);
  const [voiceData, setVoiceData] = useState<VoiceAssistantData | null>(null);
  const [aiData, setAiData] = useState<any>(null);

  // Voice recording test
  const { state: recordingState, controls: recordingControls } = useVoiceRecording({
    onTranscription: (text, confidence) => {
      addTestResult(`Transcription: "${text}" (Confidence: ${Math.round(confidence * 100)}%)`);
    },
    onError: (error) => {
      addTestResult(`Error: ${error}`);
    }
  });

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testSpeechRecognition = async () => {
    addTestResult('Testing speech recognition capability...');
    const isAvailable = await speechToTextService.testCapability();
    addTestResult(`Speech recognition available: ${isAvailable}`);
    
    if (isAvailable) {
      const languages = speechToTextService.getSupportedLanguages();
      addTestResult(`Supported languages: ${languages.slice(0, 5).join(', ')}...`);
    }
  };

  const testVoiceRecording = async () => {
    if (recordingState.isRecording) {
      addTestResult('Stopping recording...');
      const audioBlob = await recordingControls.stopRecording();
      if (audioBlob) {
        addTestResult(`Recording completed. Size: ${audioBlob.size} bytes`);
      }
    } else {
      addTestResult('Starting voice recording...');
      await recordingControls.startRecording();
    }
  };

  const handleVoiceAssistantComplete = (data: VoiceAssistantData) => {
    setVoiceData(data);
    setShowVoiceAssistant(false);
    addTestResult('Voice assistant completed successfully');
    addTestResult(`Collected data: ${JSON.stringify(data, null, 2)}`);
  };

  const handleVoiceAssistantCancel = () => {
    setShowVoiceAssistant(false);
    addTestResult('Voice assistant cancelled');
  };

  const handleAIDialogApply = (data: any) => {
    setAiData(data);
    setShowAIDialog(false);
    addTestResult('AI dialog completed successfully');
    addTestResult(`AI generated data: ${JSON.stringify(data, null, 2)}`);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Voice-Powered AI Assistant Test Page
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        This page demonstrates the voice-powered AI assistant features for issue creation.
        Test each component individually or use the full workflow.
      </Typography>

      <Grid container spacing={3}>
        {/* Basic Voice Recording Test */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Voice Recording Test
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Test basic voice recording and speech-to-text functionality.
              </Typography>
              
              {recordingState.isRecording && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="secondary">
                    Recording... Duration: {recordingState.duration}s
                  </Typography>
                  <Box sx={{ 
                    height: 20, 
                    bgcolor: 'grey.200', 
                    borderRadius: 1,
                    overflow: 'hidden',
                    mt: 1
                  }}>
                    <Box sx={{
                      height: '100%',
                      width: `${recordingState.audioLevel * 100}%`,
                      bgcolor: 'secondary.main',
                      transition: 'width 0.1s'
                    }} />
                  </Box>
                </Box>
              )}
            </CardContent>
            <CardActions>
              <Button
                variant="contained"
                startIcon={recordingState.isRecording ? <StopIcon /> : <MicIcon />}
                onClick={testVoiceRecording}
                color={recordingState.isRecording ? "secondary" : "primary"}
                disabled={recordingState.isProcessing}
              >
                {recordingState.isRecording ? 'Stop Recording' : 'Start Recording'}
              </Button>
              <Button onClick={testSpeechRecognition}>
                Test Capability
              </Button>
            </CardActions>
          </Card>
        </Grid>

        {/* Voice Assistant Test */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Voice Assistant Test
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Test the full conversational voice assistant for issue creation.
              </Typography>
              
              {voiceData && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  Voice data collected successfully!
                </Alert>
              )}
            </CardContent>
            <CardActions>
              <Button
                variant="contained"
                startIcon={<MicIcon />}
                onClick={() => setShowVoiceAssistant(true)}
                color="secondary"
              >
                Start Voice Assistant
              </Button>
              {voiceData && (
                <Button
                  variant="outlined"
                  onClick={() => setVoiceData(null)}
                >
                  Clear Data
                </Button>
              )}
            </CardActions>
          </Card>
        </Grid>

        {/* AI Dialog Test */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Enhanced AI Dialog Test
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Test the AI dialog with integrated voice capabilities.
              </Typography>
              
              {aiData && (
                <Alert severity="success" sx={{ mb: 2 }}>
                  AI assistance completed successfully!
                </Alert>
              )}
            </CardContent>
            <CardActions>
              <Button
                variant="contained"
                startIcon={<AIIcon />}
                onClick={() => setShowAIDialog(true)}
              >
                Open AI Dialog
              </Button>
              {aiData && (
                <Button
                  variant="outlined"
                  onClick={() => setAiData(null)}
                >
                  Clear Data
                </Button>
              )}
            </CardActions>
          </Card>
        </Grid>

        {/* Test Results */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Test Results
              </Typography>
              <Button onClick={clearResults} size="small">
                Clear Results
              </Button>
            </Box>
            
            <Divider sx={{ mb: 2 }} />
            
            <Box sx={{ 
              maxHeight: 300, 
              overflow: 'auto',
              bgcolor: 'grey.50',
              p: 2,
              borderRadius: 1,
              fontFamily: 'monospace',
              fontSize: '0.875rem'
            }}>
              {testResults.length === 0 ? (
                <Typography variant="body2" color="text.secondary">
                  No test results yet. Run some tests to see output here.
                </Typography>
              ) : (
                testResults.map((result, index) => (
                  <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                    {result}
                  </Typography>
                ))
              )}
            </Box>
          </Paper>
        </Grid>

        {/* Collected Data Display */}
        {(voiceData || aiData) && (
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Collected Data
              </Typography>
              
              {voiceData && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" color="secondary" gutterBottom>
                    Voice Assistant Data:
                  </Typography>
                  <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <pre style={{ margin: 0, fontSize: '0.875rem' }}>
                      {JSON.stringify(voiceData, null, 2)}
                    </pre>
                  </Paper>
                </Box>
              )}
              
              {aiData && (
                <Box>
                  <Typography variant="subtitle1" color="primary" gutterBottom>
                    AI Generated Data:
                  </Typography>
                  <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <pre style={{ margin: 0, fontSize: '0.875rem' }}>
                      {JSON.stringify(aiData, null, 2)}
                    </pre>
                  </Paper>
                </Box>
              )}
            </Paper>
          </Grid>
        )}
      </Grid>

      {/* Voice Assistant Dialog */}
      {showVoiceAssistant && (
        <VoiceAssistant
          isOpen={showVoiceAssistant}
          onComplete={handleVoiceAssistantComplete}
          onCancel={handleVoiceAssistantCancel}
        />
      )}

      {/* AI Dialog */}
      <AIAssistDialog
        open={showAIDialog}
        onClose={() => setShowAIDialog(false)}
        onApply={handleAIDialogApply}
      />
    </Container>
  );
};

export default VoiceTestPage;
