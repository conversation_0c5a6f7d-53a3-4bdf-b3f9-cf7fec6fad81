import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Container,
  Grid,
  Card,
  CardContent,
  IconButton,
  Chip,
  Avatar,
  Fade,
  Alert,
  LinearProgress,
  Divider,
  Tooltip
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  Mic as MicIcon,
  <PERSON>c<PERSON>ff as MicOffIcon,
  VolumeUp as SpeakIcon,
  Pause as PauseIcon,
  PlayArrow as PlayIcon,
  Settings as SettingsIcon,
  Chat as ChatIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import enhancedVoiceService, { ConversationMessage, ConversationState, VoiceSettings } from '../services/enhancedVoiceService';
import speechToTextService, { SpeechToTextResult } from '../services/speechToTextService';
import VoiceSettingsComponent from './VoiceSettings';

export interface VoiceAssistantData {
  issueDescription: string;
  environment?: string;
  severity?: string;
  priority?: string;
  additionalContext?: string;
}

interface EnhancedVoiceAssistantProps {
  onComplete: (data: VoiceAssistantData) => void;
  onBack: () => void;
  isOpen: boolean;
}

const EnhancedVoiceAssistant: React.FC<EnhancedVoiceAssistantProps> = ({ 
  onComplete, 
  onBack, 
  isOpen 
}) => {
  const [conversationState, setConversationState] = useState<ConversationState>(
    enhancedVoiceService.getConversationState()
  );
  const [showSettings, setShowSettings] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [audioTestResults, setAudioTestResults] = useState<{ speakers: boolean; microphone: boolean } | null>(null);
  const [wakeWordActive, setWakeWordActive] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  const stopRecognitionRef = useRef<(() => void) | null>(null);
  const wakeWordStopRef = useRef<(() => void) | null>(null);
  const conversationEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom of conversation
  useEffect(() => {
    if (conversationEndRef.current) {
      conversationEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [conversationState.conversationHistory]);

  // Initialize conversation when opened
  useEffect(() => {
    if (isOpen && conversationState.conversationHistory.length === 0) {
      startConversation();
    }
  }, [isOpen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, []);

  const cleanup = () => {
    if (stopRecognitionRef.current) {
      stopRecognitionRef.current();
      stopRecognitionRef.current = null;
    }
    if (wakeWordStopRef.current) {
      wakeWordStopRef.current();
      wakeWordStopRef.current = null;
    }
    enhancedVoiceService.stopSpeaking();
    enhancedVoiceService.cleanup();
  };

  const startConversation = async () => {
    try {
      enhancedVoiceService.resetConversation();
      
      const greeting = "Hello! I'm Waldo, your AI assistant. I'm here to help you report an issue. What problem are you experiencing?";
      
      enhancedVoiceService.addConversationMessage({
        type: 'ai',
        content: greeting,
        isGreeting: true
      });

      await enhancedVoiceService.speak(greeting, {
        interrupt: true,
        onEnd: () => {
          startListening();
        }
      });

      updateConversationState();
    } catch (error) {
      setError('Failed to start conversation');
      console.error('Conversation start error:', error);
    }
  };

  const startListening = async () => {
    if (isPaused) return;

    try {
      setError(null);
      setCurrentTranscript('');
      
      const stopFn = await speechToTextService.startRealTimeRecognition(
        (result: SpeechToTextResult) => {
          handleTranscription(result.transcript, result.confidence);
        },
        (error: string) => {
          setError(error);
          setIsListening(false);
          stopRecognitionRef.current = null;
        },
        { continuous: false, interimResults: true }
      );
      
      stopRecognitionRef.current = stopFn;
      setIsListening(true);
      
      enhancedVoiceService.updateConversationState({ 
        isListening: true, 
        awaitingResponse: true 
      });
      updateConversationState();
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to start listening');
    }
  };

  const stopListening = () => {
    if (stopRecognitionRef.current) {
      stopRecognitionRef.current();
      stopRecognitionRef.current = null;
    }
    setIsListening(false);
    enhancedVoiceService.updateConversationState({ 
      isListening: false, 
      awaitingResponse: false 
    });
    updateConversationState();
  };

  const handleTranscription = async (transcript: string, confidence: number) => {
    setCurrentTranscript(transcript);
    
    // Only process final results
    if (confidence < 0.7) return;
    
    stopListening();
    
    // Add user message to conversation
    enhancedVoiceService.addConversationMessage({
      type: 'user',
      content: transcript,
      confidence
    });

    // Analyze user input
    const analysis = enhancedVoiceService.analyzeUserInput(transcript, confidence);
    
    // Handle different types of input
    if (analysis.isCommand) {
      await handleVoiceCommand(analysis.intent, transcript);
    } else if (analysis.isGreeting) {
      await handleGreeting(transcript);
    } else if (analysis.isIssueDescription) {
      await handleIssueData(transcript, analysis.extractedData);
    } else {
      await handleUnknownInput(transcript);
    }
    
    updateConversationState();
  };

  const handleVoiceCommand = async (intent: string, transcript: string) => {
    let response = '';
    
    switch (intent) {
      case 'repeat':
        const lastAiMessage = conversationState.conversationHistory
          .filter(msg => msg.type === 'ai')
          .pop();
        if (lastAiMessage) {
          response = lastAiMessage.content;
        } else {
          response = "I don't have anything to repeat. Let me know what problem you're experiencing.";
        }
        break;
        
      case 'previous':
        response = "Let me go back to the previous question.";
        // Implement step navigation logic here
        break;
        
      case 'pause':
        setIsPaused(true);
        response = "Conversation paused. Click the play button or say 'continue' when you're ready.";
        break;
        
      case 'restart':
        enhancedVoiceService.resetConversation();
        await startConversation();
        return;
        
      default:
        response = "I understand. How can I help you with that?";
    }
    
    enhancedVoiceService.addConversationMessage({
      type: 'ai',
      content: response
    });
    
    await enhancedVoiceService.speak(response, {
      onEnd: () => {
        if (!isPaused) startListening();
      }
    });
  };

  const handleGreeting = async (transcript: string) => {
    const response = "Hello! I can hear you clearly. Now, what issue would you like to report?";
    
    enhancedVoiceService.addConversationMessage({
      type: 'ai',
      content: response
    });
    
    await enhancedVoiceService.speak(response, {
      onEnd: () => startListening()
    });
  };

  const handleIssueData = async (transcript: string, extractedData: any) => {
    // Store the issue data
    const currentData = enhancedVoiceService.getConversationState().collectedData;
    const updatedData = { ...currentData, ...extractedData, issueDescription: transcript };
    
    enhancedVoiceService.updateConversationState({
      collectedData: updatedData,
      phase: 'issue_collection'
    });
    
    // Generate intelligent follow-up response
    const response = enhancedVoiceService.generateResponse(transcript, updatedData);
    
    enhancedVoiceService.addConversationMessage({
      type: 'ai',
      content: response,
      isIssueData: true
    });
    
    // Check if we have enough information
    const hasEnvironment = updatedData.environment;
    const hasSeverity = updatedData.severity;
    const hasPriority = updatedData.priority;
    
    if (hasEnvironment && hasSeverity && hasPriority) {
      // We have enough information, move to confirmation
      const confirmationResponse = `Perfect! I have all the information I need. Let me summarize: 
        You're reporting "${transcript}" in ${updatedData.environment} environment, 
        with ${updatedData.severity} severity and ${updatedData.priority} priority. 
        Is this correct, or would you like to add anything else?`;
      
      enhancedVoiceService.addConversationMessage({
        type: 'ai',
        content: confirmationResponse
      });
      
      enhancedVoiceService.updateConversationState({ phase: 'confirmation' });
      
      await enhancedVoiceService.speak(confirmationResponse, {
        onEnd: () => startListening()
      });
    } else {
      await enhancedVoiceService.speak(response, {
        onEnd: () => startListening()
      });
    }
  };

  const handleUnknownInput = async (transcript: string) => {
    const response = "I'm not sure I understood that completely. Could you please tell me more about the issue you're experiencing?";
    
    enhancedVoiceService.addConversationMessage({
      type: 'ai',
      content: response
    });
    
    await enhancedVoiceService.speak(response, {
      onEnd: () => startListening()
    });
  };

  const updateConversationState = () => {
    setConversationState(enhancedVoiceService.getConversationState());
  };

  const handleComplete = () => {
    const data = conversationState.collectedData;
    const voiceData: VoiceAssistantData = {
      issueDescription: data.issueDescription || '',
      environment: data.environment,
      severity: data.severity,
      priority: data.priority,
      additionalContext: data.additionalContext
    };
    
    cleanup();
    onComplete(voiceData);
  };

  const togglePause = () => {
    if (isPaused) {
      setIsPaused(false);
      startListening();
    } else {
      setIsPaused(true);
      stopListening();
      enhancedVoiceService.stopSpeaking();
    }
  };

  const handleSettingsChange = (settings: VoiceSettings) => {
    // Settings are automatically saved by the service
    console.log('Voice settings updated:', settings);
  };

  const handleAudioTest = (results: { speakers: boolean; microphone: boolean }) => {
    setAudioTestResults(results);
  };

  if (showSettings) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={() => setShowSettings(false)} sx={{ mr: 2 }}>
            <BackIcon />
          </IconButton>
          <Typography variant="h5">Voice Assistant Settings</Typography>
        </Box>
        
        <VoiceSettingsComponent 
          onSettingsChange={handleSettingsChange}
          onTestComplete={handleAudioTest}
        />
        
        <Box sx={{ mt: 3, textAlign: 'center' }}>
          <Button 
            variant="contained" 
            onClick={() => setShowSettings(false)}
            size="large"
          >
            Save Settings & Continue
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 2, height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <IconButton onClick={onBack} sx={{ mr: 2 }}>
            <BackIcon />
          </IconButton>
          <Typography variant="h4" component="h1">
            Voice Assistant
          </Typography>
          <Chip 
            label={conversationState.phase.replace('_', ' ').toUpperCase()} 
            color="primary" 
            sx={{ ml: 2 }}
          />
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Voice Settings">
            <IconButton onClick={() => setShowSettings(true)}>
              <SettingsIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title={isPaused ? "Resume Conversation" : "Pause Conversation"}>
            <IconButton onClick={togglePause} color={isPaused ? "primary" : "default"}>
              {isPaused ? <PlayIcon /> : <PauseIcon />}
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Restart Conversation">
            <IconButton onClick={startConversation}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Audio Test Alert */}
      {audioTestResults && (!audioTestResults.speakers || !audioTestResults.microphone) && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          Audio setup issue detected. Please check your {!audioTestResults.speakers ? 'speakers' : ''} 
          {!audioTestResults.speakers && !audioTestResults.microphone ? ' and ' : ''}
          {!audioTestResults.microphone ? 'microphone' : ''} settings.
        </Alert>
      )}

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3} sx={{ flexGrow: 1 }}>
        {/* Conversation Area */}
        <Grid item xs={12} md={8}>
          <Paper 
            elevation={2} 
            sx={{ 
              height: '60vh', 
              display: 'flex', 
              flexDirection: 'column',
              overflow: 'hidden'
            }}
          >
            {/* Conversation Header */}
            <Box sx={{ p: 2, bgcolor: 'primary.50', borderBottom: 1, borderColor: 'divider' }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <ChatIcon sx={{ mr: 1 }} />
                <Typography variant="h6">Conversation with Waldo</Typography>
                {conversationState.isSpeaking && (
                  <Chip label="Speaking..." size="small" sx={{ ml: 2 }} />
                )}
                {isListening && (
                  <Chip label="Listening..." color="secondary" size="small" sx={{ ml: 2 }} />
                )}
              </Box>
            </Box>

            {/* Messages */}
            <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
              {conversationState.conversationHistory.map((message, index) => (
                <Box key={message.id} sx={{ mb: 2 }}>
                  <Box sx={{ 
                    display: 'flex', 
                    justifyContent: message.type === 'ai' ? 'flex-start' : 'flex-end',
                    alignItems: 'flex-start',
                    gap: 1
                  }}>
                    {message.type === 'ai' && (
                      <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
                        W
                      </Avatar>
                    )}
                    
                    <Paper
                      elevation={1}
                      sx={{
                        p: 2,
                        maxWidth: '70%',
                        bgcolor: message.type === 'ai' ? 'grey.100' : 'primary.100',
                        borderRadius: 2
                      }}
                    >
                      <Typography variant="body1">
                        {message.content}
                      </Typography>
                      
                      {message.confidence && (
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          Confidence: {Math.round(message.confidence * 100)}%
                        </Typography>
                      )}
                    </Paper>
                    
                    {message.type === 'user' && (
                      <Avatar sx={{ bgcolor: 'secondary.main', width: 32, height: 32 }}>
                        U
                      </Avatar>
                    )}
                  </Box>
                </Box>
              ))}
              
              {/* Current transcript preview */}
              {currentTranscript && isListening && (
                <Fade in={true}>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                    <Paper
                      elevation={1}
                      sx={{
                        p: 2,
                        maxWidth: '70%',
                        bgcolor: 'secondary.50',
                        borderRadius: 2,
                        border: '2px dashed',
                        borderColor: 'secondary.main'
                      }}
                    >
                      <Typography variant="body1" color="secondary.main">
                        {currentTranscript}...
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Listening...
                      </Typography>
                    </Paper>
                  </Box>
                </Fade>
              )}
              
              <div ref={conversationEndRef} />
            </Box>

            {/* Voice Controls */}
            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', bgcolor: 'grey.50' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2 }}>
                <IconButton
                  size="large"
                  color={isListening ? "secondary" : "primary"}
                  onClick={isListening ? stopListening : startListening}
                  disabled={conversationState.isSpeaking || isPaused}
                  sx={{
                    width: 64,
                    height: 64,
                    bgcolor: isListening ? 'secondary.main' : 'primary.main',
                    color: 'white',
                    '&:hover': {
                      bgcolor: isListening ? 'secondary.dark' : 'primary.dark',
                    },
                    animation: isListening ? 'pulse 1.5s infinite' : 'none',
                    '@keyframes pulse': {
                      '0%': { transform: 'scale(1)' },
                      '50%': { transform: 'scale(1.1)' },
                      '100%': { transform: 'scale(1)' }
                    }
                  }}
                >
                  {isListening ? <MicIcon fontSize="large" /> : <MicOffIcon fontSize="large" />}
                </IconButton>
                
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {isPaused ? 'Conversation Paused' : 
                     isListening ? 'Listening... Speak now' : 
                     conversationState.isSpeaking ? 'Waldo is speaking...' : 
                     'Click to start listening'}
                  </Typography>
                  
                  {isListening && (
                    <LinearProgress 
                      sx={{ mt: 1, width: 200 }} 
                      color="secondary"
                    />
                  )}
                </Box>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Issue Summary Panel */}
        <Grid item xs={12} md={4}>
          <Paper elevation={2} sx={{ p: 3, height: '60vh', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom>
              Issue Summary
            </Typography>
            
            <Divider sx={{ mb: 2 }} />
            
            {conversationState.collectedData.issueDescription ? (
              <Box>
                <Typography variant="subtitle2" color="primary" gutterBottom>
                  Description:
                </Typography>
                <Typography variant="body2" paragraph>
                  {conversationState.collectedData.issueDescription}
                </Typography>
                
                {conversationState.collectedData.environment && (
                  <>
                    <Typography variant="subtitle2" color="primary" gutterBottom>
                      Environment:
                    </Typography>
                    <Chip 
                      label={conversationState.collectedData.environment} 
                      size="small" 
                      sx={{ mb: 2 }}
                    />
                  </>
                )}
                
                {conversationState.collectedData.severity && (
                  <>
                    <Typography variant="subtitle2" color="primary" gutterBottom>
                      Severity:
                    </Typography>
                    <Chip 
                      label={conversationState.collectedData.severity} 
                      size="small" 
                      color="error"
                      sx={{ mb: 2 }}
                    />
                  </>
                )}
                
                {conversationState.collectedData.priority && (
                  <>
                    <Typography variant="subtitle2" color="primary" gutterBottom>
                      Priority:
                    </Typography>
                    <Chip 
                      label={conversationState.collectedData.priority} 
                      size="small" 
                      color="warning"
                      sx={{ mb: 2 }}
                    />
                  </>
                )}
                
                {conversationState.phase === 'confirmation' && (
                  <Box sx={{ mt: 3 }}>
                    <Button
                      variant="contained"
                      fullWidth
                      onClick={handleComplete}
                      startIcon={<CheckIcon />}
                      size="large"
                    >
                      Create Issue Report
                    </Button>
                  </Box>
                )}
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">
                Start describing your issue and I'll capture the details here.
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default EnhancedVoiceAssistant;
