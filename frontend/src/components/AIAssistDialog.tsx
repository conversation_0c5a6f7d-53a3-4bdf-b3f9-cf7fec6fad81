import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  CircularProgress,
  Alert,
  Divider,
  Paper,
  Tabs,
  Tab,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Mic as MicIcon,
  Keyboard as KeyboardIcon,
  VolumeUp as SpeakIcon
} from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import { generateAIIssueDescription, clearAIAssistance } from '../store/slices/issueSlice';
import VoiceAssistant, { VoiceAssistantData } from './VoiceAssistant';

interface AIAssistDialogProps {
  open: boolean;
  onClose: () => void;
  onApply: (aiData: any) => void;
}

const AIAssistDialog: React.FC<AIAssistDialogProps> = ({ open, onClose, onApply }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { aiAssistance, aiLoading, error } = useSelector((state: RootState) => state.issues);

  const [briefDescription, setBriefDescription] = useState('');
  const [context, setContext] = useState('');
  const [useAdvancedAI, setUseAdvancedAI] = useState(true);
  const [showPreview, setShowPreview] = useState(false);
  const [inputMode, setInputMode] = useState<'text' | 'voice'>('text');
  const [voiceData, setVoiceData] = useState<VoiceAssistantData | null>(null);
  const [showVoiceAssistant, setShowVoiceAssistant] = useState(false);

  const handleGenerate = async () => {
    if (!briefDescription.trim()) {
      return;
    }

    try {
      await dispatch(generateAIIssueDescription({
        briefDescription: briefDescription.trim(),
        context: context.trim() || undefined,
        useAdvancedAI
      })).unwrap();
      setShowPreview(true);
    } catch (error) {
      console.error('Failed to generate AI assistance:', error);
    }
  };

  const handleApply = () => {
    if (aiAssistance) {
      onApply(aiAssistance);
      handleClose();
    }
  };

  const handleClose = () => {
    setBriefDescription('');
    setContext('');
    setShowPreview(false);
    dispatch(clearAIAssistance());
    onClose();
  };

  const handleReset = () => {
    setShowPreview(false);
    setVoiceData(null);
    setShowVoiceAssistant(false);
    dispatch(clearAIAssistance());
  };

  // Handle voice assistant completion
  const handleVoiceComplete = async (data: VoiceAssistantData) => {
    setVoiceData(data);
    setShowVoiceAssistant(false);

    // Populate text fields with voice data
    setBriefDescription(data.issueDescription);
    setContext(data.additionalContext || '');

    // Automatically generate AI assistance with voice data
    try {
      await dispatch(generateAIIssueDescription({
        briefDescription: data.issueDescription,
        context: data.additionalContext,
        environment: data.environment,
        useAdvancedAI
      })).unwrap();
      setShowPreview(true);
    } catch (error) {
      console.error('Failed to generate AI assistance from voice data:', error);
    }
  };

  // Handle voice assistant cancellation
  const handleVoiceCancel = () => {
    setShowVoiceAssistant(false);
    setVoiceData(null);
  };

  // Start voice assistant
  const startVoiceAssistant = () => {
    setShowVoiceAssistant(true);
  };

  // Handle input mode change
  const handleInputModeChange = (event: React.SyntheticEvent, newValue: 'text' | 'voice') => {
    setInputMode(newValue);
    if (newValue === 'voice') {
      startVoiceAssistant();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { minHeight: '500px' }
      }}
    >
      <DialogTitle>
        <Typography variant="h6" component="div">
          AI-Assisted Issue Creation
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Choose your preferred input method: type or speak your issue description
        </Typography>

        {/* Input mode tabs */}
        <Box sx={{ mt: 2 }}>
          <Tabs
            value={inputMode}
            onChange={handleInputModeChange}
            variant="fullWidth"
            sx={{ minHeight: 40 }}
          >
            <Tab
              value="text"
              icon={<KeyboardIcon />}
              label="Text Input"
              sx={{ minHeight: 40 }}
            />
            <Tab
              value="voice"
              icon={<MicIcon />}
              label="Voice Assistant"
              sx={{ minHeight: 40 }}
            />
          </Tabs>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {showVoiceAssistant ? (
          <VoiceAssistant
            isOpen={showVoiceAssistant}
            onComplete={handleVoiceComplete}
            onCancel={handleVoiceCancel}
          />
        ) : !showPreview ? (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            {/* Voice data summary if available */}
            {voiceData && (
              <Paper elevation={1} sx={{ p: 2, bgcolor: 'success.50', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <MicIcon color="success" sx={{ mr: 1 }} />
                  <Typography variant="subtitle2" color="success.main">
                    Voice Input Captured
                  </Typography>
                  <Tooltip title="Start voice assistant again">
                    <IconButton
                      size="small"
                      onClick={startVoiceAssistant}
                      sx={{ ml: 'auto' }}
                    >
                      <MicIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Issue:</strong> {voiceData.issueDescription}
                </Typography>
                {voiceData.environment && (
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Environment:</strong> {voiceData.environment}
                  </Typography>
                )}
                {voiceData.severity && (
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Severity:</strong> {voiceData.severity}
                  </Typography>
                )}
                {voiceData.priority && (
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Priority:</strong> {voiceData.priority}
                  </Typography>
                )}
              </Paper>
            )}

            <TextField
              fullWidth
              label="Brief Issue Description"
              placeholder="e.g., Login button not working on mobile devices"
              multiline
              rows={3}
              value={briefDescription}
              onChange={(e) => setBriefDescription(e.target.value)}
              helperText="Provide a brief description of the issue (10-1000 characters)"
              error={briefDescription.length > 0 && briefDescription.length < 10}
              InputProps={{
                endAdornment: (
                  <Tooltip title="Use voice input">
                    <IconButton onClick={startVoiceAssistant} edge="end">
                      <MicIcon />
                    </IconButton>
                  </Tooltip>
                )
              }}
            />

            <TextField
              fullWidth
              label="Additional Context (Optional)"
              placeholder="e.g., This happens only on iOS Safari, started after last update"
              multiline
              rows={2}
              value={context}
              onChange={(e) => setContext(e.target.value)}
              helperText="Any additional context that might help generate better suggestions"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={useAdvancedAI}
                  onChange={(e) => setUseAdvancedAI(e.target.checked)}
                />
              }
              label={
                <Box>
                  <Typography variant="body2">Use Advanced AI (ChatGPT)</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {useAdvancedAI
                      ? "Uses ChatGPT for more detailed and accurate suggestions"
                      : "Uses basic NLP for faster but simpler suggestions"
                    }
                  </Typography>
                </Box>
              }
            />

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </Box>
        ) : (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6" color="primary">
                AI Generated Suggestions
              </Typography>
              <Button onClick={handleReset} size="small">
                Generate Again
              </Button>
            </Box>

            {aiAssistance && (
              <Paper elevation={1} sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box>
                    <Typography variant="subtitle2" color="primary">Title:</Typography>
                    <Typography variant="body2">{aiAssistance.title}</Typography>
                  </Box>

                  <Divider />

                  <Box>
                    <Typography variant="subtitle2" color="primary">Type:</Typography>
                    <Typography variant="body2">{aiAssistance.type}</Typography>
                  </Box>

                  <Box>
                    <Typography variant="subtitle2" color="primary">Severity:</Typography>
                    <Typography variant="body2">{aiAssistance.severity}</Typography>
                  </Box>

                  <Box>
                    <Typography variant="subtitle2" color="primary">Priority:</Typography>
                    <Typography variant="body2">{aiAssistance.priority}</Typography>
                  </Box>

                  <Divider />

                  <Box>
                    <Typography variant="subtitle2" color="primary">Description:</Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        whiteSpace: 'pre-line',
                        maxHeight: '200px',
                        overflow: 'auto',
                        p: 1,
                        bgcolor: 'white',
                        border: '1px solid',
                        borderColor: 'grey.300',
                        borderRadius: 1
                      }}
                    >
                      {aiAssistance.description}
                    </Typography>
                  </Box>

                  {aiAssistance.confidence && (
                    <Box>
                      <Typography variant="caption" color="text.secondary">
                        Confidence: {Math.round(aiAssistance.confidence * 100)}%
                        {aiAssistance.usedAdvancedAI ? ' (Advanced AI)' : ' (Basic NLP)'}
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2, gap: 1 }}>
        <Button onClick={handleClose}>
          Cancel
        </Button>

        {showVoiceAssistant ? (
          // Voice assistant is handling its own actions
          null
        ) : !showPreview ? (
          <>
            <Button
              variant="outlined"
              startIcon={<MicIcon />}
              onClick={startVoiceAssistant}
              sx={{ mr: 'auto' }}
            >
              Voice Assistant
            </Button>
            <Button
              variant="contained"
              onClick={handleGenerate}
              disabled={aiLoading || briefDescription.length < 10}
              startIcon={aiLoading ? <CircularProgress size={16} /> : null}
            >
              {aiLoading ? 'Generating...' : 'Generate Suggestions'}
            </Button>
          </>
        ) : (
          <Button
            variant="contained"
            onClick={handleApply}
            disabled={!aiAssistance}
          >
            Apply to Form
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AIAssistDialog;
