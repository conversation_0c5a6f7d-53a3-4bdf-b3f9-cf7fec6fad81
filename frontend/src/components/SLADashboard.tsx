import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  Alert,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  LineChart,
  Line
} from 'recharts';
import { format } from 'date-fns';
import api from '../services/apiService';

interface SlaStatistics {
  totalSlas: number;
  activeSlas: number;
  completedSlas: number;
  responseBreaches: number;
  resolutionBreaches: number;
  escalatedSlas: number;
  averageResponseTimeHours: number;
  averageResolutionTimeHours: number;
  compliancePercentage: number;
}

interface SlaTracking {
  id: number;
  issue: {
    id: number;
    identifier: string;
    title: string;
    severity: string;
    priority: string;
    status: string;
  };
  responseDueDate: string;
  resolutionDueDate: string;
  escalationDueDate: string;
  responseDate?: string;
  resolutionDate?: string;
  slaStatus: string;
  breachReason?: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const SLADashboard: React.FC = () => {
  const [statistics, setStatistics] = useState<SlaStatistics | null>(null);
  const [breaches, setBreaches] = useState<SlaTracking[]>([]);
  const [upcomingDeadlines, setUpcomingDeadlines] = useState<SlaTracking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSlaData();
  }, []);

  const fetchSlaData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [statsResponse, breachesResponse, deadlinesResponse] = await Promise.all([
        api.get('/api/sla/statistics'),
        api.get('/api/sla/breaches'),
        api.get('/api/sla/deadlines/upcoming?hours=24')
      ]);

      setStatistics(statsResponse.data);
      setBreaches(breachesResponse.data);
      setUpcomingDeadlines(deadlinesResponse.data);
    } catch (err: any) {
      console.error('Error fetching SLA data:', err);
      setError('Failed to load SLA data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const getSlaStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'primary';
      case 'COMPLETED': return 'success';
      case 'RESPONSE_BREACHED': return 'warning';
      case 'RESOLUTION_BREACHED': return 'error';
      case 'ESCALATED': return 'error';
      default: return 'default';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return '#FF4444';
      case 'MAJOR': return '#FF8800';
      case 'NORMAL': return '#4CAF50';
      case 'MINOR': return '#2196F3';
      default: return '#9E9E9E';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!statistics) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No SLA data available.
      </Alert>
    );
  }

  const complianceData = [
    { name: 'Compliant', value: statistics.compliancePercentage, color: '#4CAF50' },
    { name: 'Breached', value: 100 - statistics.compliancePercentage, color: '#FF4444' }
  ];

  const slaStatusData = [
    { name: 'Active', value: statistics.activeSlas },
    { name: 'Completed', value: statistics.completedSlas },
    { name: 'Response Breached', value: statistics.responseBreaches },
    { name: 'Resolution Breached', value: statistics.resolutionBreaches },
    { name: 'Escalated', value: statistics.escalatedSlas }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        SLA Dashboard
      </Typography>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                SLA Compliance
              </Typography>
              <Typography variant="h4" component="div">
                {statistics.compliancePercentage.toFixed(1)}%
              </Typography>
              <LinearProgress 
                variant="determinate" 
                value={statistics.compliancePercentage} 
                sx={{ mt: 1 }}
                color={statistics.compliancePercentage >= 95 ? 'success' : statistics.compliancePercentage >= 80 ? 'warning' : 'error'}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Active SLAs
              </Typography>
              <Typography variant="h4" component="div">
                {statistics.activeSlas}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                of {statistics.totalSlas} total
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg Response Time
              </Typography>
              <Typography variant="h4" component="div">
                {statistics.averageResponseTimeHours?.toFixed(1) || 'N/A'}h
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Avg Resolution Time
              </Typography>
              <Typography variant="h4" component="div">
                {statistics.averageResolutionTimeHours?.toFixed(1) || 'N/A'}h
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* SLA Compliance Chart */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              SLA Compliance
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <PieChart>
                <Pie
                  data={complianceData}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {complianceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* SLA Status Distribution */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, height: '400px' }}>
            <Typography variant="h6" gutterBottom>
              SLA Status Distribution
            </Typography>
            <ResponsiveContainer width="100%" height="90%">
              <BarChart data={slaStatusData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* SLA Breaches */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom>
              Current SLA Breaches ({breaches.length})
            </Typography>
            {breaches.length === 0 ? (
              <Typography color="textSecondary" sx={{ textAlign: 'center', mt: 4 }}>
                No SLA breaches found
              </Typography>
            ) : (
              <List dense>
                {breaches.map((breach) => (
                  <ListItem key={breach.id} divider>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="subtitle2">
                            {breach.issue.identifier}
                          </Typography>
                          <Chip 
                            label={breach.issue.severity} 
                            size="small" 
                            sx={{ backgroundColor: getSeverityColor(breach.issue.severity), color: 'white' }}
                          />
                          <Chip 
                            label={breach.slaStatus} 
                            size="small" 
                            color={getSlaStatusColor(breach.slaStatus) as any}
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" noWrap>
                            {breach.issue.title}
                          </Typography>
                          {breach.breachReason && (
                            <Typography variant="caption" color="error">
                              {breach.breachReason}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Upcoming Deadlines */}
        <Grid item xs={12} md={6}>
          <Paper elevation={2} sx={{ p: 2, height: '400px', overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom>
              Upcoming Deadlines (24h) ({upcomingDeadlines.length})
            </Typography>
            {upcomingDeadlines.length === 0 ? (
              <Typography color="textSecondary" sx={{ textAlign: 'center', mt: 4 }}>
                No upcoming deadlines
              </Typography>
            ) : (
              <List dense>
                {upcomingDeadlines.map((deadline) => (
                  <ListItem key={deadline.id} divider>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="subtitle2">
                            {deadline.issue.identifier}
                          </Typography>
                          <Chip 
                            label={deadline.issue.severity} 
                            size="small" 
                            sx={{ backgroundColor: getSeverityColor(deadline.issue.severity), color: 'white' }}
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" noWrap>
                            {deadline.issue.title}
                          </Typography>
                          <Typography variant="caption" color="warning.main">
                            Due: {format(new Date(deadline.resolutionDueDate), 'MMM dd, HH:mm')}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SLADashboard;
