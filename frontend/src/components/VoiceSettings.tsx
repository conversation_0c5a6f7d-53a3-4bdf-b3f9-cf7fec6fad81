import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Button,
  Paper,
  Grid,
  Alert,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Mic as MicIcon,
  VolumeUp as VolumeIcon,
  Speed as SpeedIcon,
  GraphicEq as PitchIcon
} from '@mui/icons-material';
import enhancedVoiceService, { VoiceSettings } from '../services/enhancedVoiceService';

interface VoiceSettingsProps {
  onSettingsChange?: (settings: VoiceSettings) => void;
  onTestComplete?: (results: { speakers: boolean; microphone: boolean }) => void;
}

const VoiceSettingsComponent: React.FC<VoiceSettingsProps> = ({ 
  onSettingsChange, 
  onTestComplete 
}) => {
  const [settings, setSettings] = useState<VoiceSettings>(enhancedVoiceService.getVoiceSettings());
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [isTestingAudio, setIsTestingAudio] = useState(false);
  const [audioTestResults, setAudioTestResults] = useState<{ speakers: boolean; microphone: boolean } | null>(null);
  const [isPreviewPlaying, setIsPreviewPlaying] = useState(false);
  const [wakeWordEnabled, setWakeWordEnabled] = useState(true);

  useEffect(() => {
    // Load available voices
    const voices = enhancedVoiceService.getAvailableVoices();
    setAvailableVoices(voices);

    // Listen for voices loaded event
    const handleVoicesChanged = () => {
      const updatedVoices = enhancedVoiceService.getAvailableVoices();
      setAvailableVoices(updatedVoices);
    };

    if ('speechSynthesis' in window) {
      window.speechSynthesis.onvoiceschanged = handleVoicesChanged;
    }

    return () => {
      if ('speechSynthesis' in window) {
        window.speechSynthesis.onvoiceschanged = null;
      }
    };
  }, []);

  const handleSettingChange = (key: keyof VoiceSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    enhancedVoiceService.updateVoiceSettings({ [key]: value });
    onSettingsChange?.(newSettings);
  };

  const testAudioSetup = async () => {
    setIsTestingAudio(true);
    try {
      const results = await enhancedVoiceService.testAudio();
      setAudioTestResults(results);
      onTestComplete?.(results);
    } catch (error) {
      console.error('Audio test failed:', error);
      setAudioTestResults({ speakers: false, microphone: false });
    } finally {
      setIsTestingAudio(false);
    }
  };

  const previewVoice = async () => {
    if (isPreviewPlaying) {
      enhancedVoiceService.stopSpeaking();
      setIsPreviewPlaying(false);
      return;
    }

    setIsPreviewPlaying(true);
    try {
      await enhancedVoiceService.speak(
        "Hello! I'm Waldo, your AI assistant. This is how I sound with your current voice settings.",
        {
          interrupt: true,
          onEnd: () => setIsPreviewPlaying(false)
        }
      );
    } catch (error) {
      console.error('Voice preview failed:', error);
      setIsPreviewPlaying(false);
    }
  };

  const getVoicesByLanguage = () => {
    const grouped: { [key: string]: SpeechSynthesisVoice[] } = {};
    availableVoices.forEach(voice => {
      const lang = voice.lang.split('-')[0];
      if (!grouped[lang]) grouped[lang] = [];
      grouped[lang].push(voice);
    });
    return grouped;
  };

  const voicesByLanguage = getVoicesByLanguage();

  return (
    <Paper elevation={2} sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Voice Assistant Settings
      </Typography>

      {/* Audio Test Section */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle2" gutterBottom>
          Audio Setup Test
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Button
            variant="outlined"
            onClick={testAudioSetup}
            disabled={isTestingAudio}
            startIcon={<VolumeIcon />}
          >
            {isTestingAudio ? 'Testing...' : 'Test Audio Setup'}
          </Button>
          
          {audioTestResults && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Alert 
                severity={audioTestResults.speakers ? 'success' : 'error'} 
                sx={{ py: 0 }}
              >
                Speakers: {audioTestResults.speakers ? 'Working' : 'Failed'}
              </Alert>
              <Alert 
                severity={audioTestResults.microphone ? 'success' : 'error'} 
                sx={{ py: 0 }}
              >
                Microphone: {audioTestResults.microphone ? 'Working' : 'Failed'}
              </Alert>
            </Box>
          )}
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Voice Selection */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>AI Voice</InputLabel>
            <Select
              value={settings.selectedVoice}
              label="AI Voice"
              onChange={(e) => handleSettingChange('selectedVoice', e.target.value)}
            >
              {Object.entries(voicesByLanguage).map(([lang, voices]) => [
                <MenuItem key={`header-${lang}`} disabled sx={{ fontWeight: 'bold' }}>
                  {lang.toUpperCase()} Voices
                </MenuItem>,
                ...voices.map(voice => (
                  <MenuItem key={voice.name} value={voice.name}>
                    {voice.name} {voice.gender ? `(${voice.gender})` : ''}
                  </MenuItem>
                ))
              ])}
            </Select>
          </FormControl>
          
          <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
            <Button
              size="small"
              variant="outlined"
              onClick={previewVoice}
              startIcon={isPreviewPlaying ? <StopIcon /> : <PlayIcon />}
              disabled={!settings.selectedVoice}
            >
              {isPreviewPlaying ? 'Stop Preview' : 'Preview Voice'}
            </Button>
          </Box>
        </Grid>

        {/* Language Selection */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Language</InputLabel>
            <Select
              value={settings.language}
              label="Language"
              onChange={(e) => handleSettingChange('language', e.target.value)}
            >
              <MenuItem value="en-US">English (US)</MenuItem>
              <MenuItem value="en-GB">English (UK)</MenuItem>
              <MenuItem value="en-AU">English (Australia)</MenuItem>
              <MenuItem value="es-ES">Spanish (Spain)</MenuItem>
              <MenuItem value="es-MX">Spanish (Mexico)</MenuItem>
              <MenuItem value="fr-FR">French</MenuItem>
              <MenuItem value="de-DE">German</MenuItem>
              <MenuItem value="it-IT">Italian</MenuItem>
              <MenuItem value="pt-BR">Portuguese (Brazil)</MenuItem>
              <MenuItem value="ja-JP">Japanese</MenuItem>
              <MenuItem value="ko-KR">Korean</MenuItem>
              <MenuItem value="zh-CN">Chinese (Simplified)</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Voice Speed */}
        <Grid item xs={12} md={4}>
          <Box sx={{ px: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <SpeedIcon sx={{ mr: 1 }} />
              <Typography variant="body2">
                Speech Speed: {settings.rate.toFixed(1)}x
              </Typography>
            </Box>
            <Slider
              value={settings.rate}
              onChange={(_, value) => handleSettingChange('rate', value)}
              min={0.5}
              max={2.0}
              step={0.1}
              marks={[
                { value: 0.5, label: '0.5x' },
                { value: 1.0, label: '1x' },
                { value: 2.0, label: '2x' }
              ]}
            />
          </Box>
        </Grid>

        {/* Voice Pitch */}
        <Grid item xs={12} md={4}>
          <Box sx={{ px: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <PitchIcon sx={{ mr: 1 }} />
              <Typography variant="body2">
                Voice Pitch: {settings.pitch.toFixed(1)}
              </Typography>
            </Box>
            <Slider
              value={settings.pitch}
              onChange={(_, value) => handleSettingChange('pitch', value)}
              min={0.5}
              max={2.0}
              step={0.1}
              marks={[
                { value: 0.5, label: 'Low' },
                { value: 1.0, label: 'Normal' },
                { value: 2.0, label: 'High' }
              ]}
            />
          </Box>
        </Grid>

        {/* Voice Volume */}
        <Grid item xs={12} md={4}>
          <Box sx={{ px: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <VolumeIcon sx={{ mr: 1 }} />
              <Typography variant="body2">
                Volume: {Math.round(settings.volume * 100)}%
              </Typography>
            </Box>
            <Slider
              value={settings.volume}
              onChange={(_, value) => handleSettingChange('volume', value)}
              min={0.1}
              max={1.0}
              step={0.1}
              marks={[
                { value: 0.1, label: '10%' },
                { value: 0.5, label: '50%' },
                { value: 1.0, label: '100%' }
              ]}
            />
          </Box>
        </Grid>

        {/* Wake Word Settings */}
        <Grid item xs={12}>
          <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={wakeWordEnabled}
                  onChange={(e) => setWakeWordEnabled(e.target.checked)}
                />
              }
              label={
                <Box>
                  <Typography variant="body2">Enable Wake Word Detection</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Say "Hey Waldo" to start conversations hands-free
                  </Typography>
                </Box>
              }
            />
            
            {wakeWordEnabled && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Supported wake phrases: "Hey Waldo", "Hello Waldo", "Start Assistant"
                </Typography>
              </Box>
            )}
          </Box>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', pt: 2 }}>
            <Button
              variant="outlined"
              onClick={() => {
                const defaultSettings = {
                  selectedVoice: '',
                  rate: 0.9,
                  pitch: 1.0,
                  volume: 0.8,
                  language: 'en-US'
                };
                setSettings(defaultSettings);
                enhancedVoiceService.updateVoiceSettings(defaultSettings);
                onSettingsChange?.(defaultSettings);
              }}
            >
              Reset to Defaults
            </Button>
            
            <Button
              variant="contained"
              onClick={previewVoice}
              startIcon={<PlayIcon />}
              disabled={!settings.selectedVoice}
            >
              Test Current Settings
            </Button>
          </Box>
        </Grid>
      </Grid>

      {/* Help Text */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'info.50', borderRadius: 1 }}>
        <Typography variant="body2" color="info.main">
          <strong>Tips:</strong>
          <br />
          • Test your audio setup before starting a conversation
          • Choose a voice that's clear and easy to understand
          • Adjust speed based on your preference (slower for better comprehension)
          • Enable wake word detection for hands-free activation
        </Typography>
      </Box>
    </Paper>
  );
};

export default VoiceSettingsComponent;
