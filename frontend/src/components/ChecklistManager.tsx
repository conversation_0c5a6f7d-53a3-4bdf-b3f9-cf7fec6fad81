import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Tooltip,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Checkbox,
  LinearProgress,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Close as CloseIcon,
  CheckBox as CheckBoxIcon,
  Delete as DeleteIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import checklistService, { ChecklistItem, ChecklistStats } from '../services/checklistService';
import { useAppSelector } from '../store/hooks';

interface ChecklistManagerProps {
  issueId: number;
  onChecklistChange?: () => void;
}

const ChecklistManager: React.FC<ChecklistManagerProps> = ({
  issueId,
  onChecklistChange
}) => {
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState<ChecklistItem[]>([]);
  const [stats, setStats] = useState<ChecklistStats>({ completed: 0, total: 0 });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newItemTitle, setNewItemTitle] = useState('');
  const [newItemDescription, setNewItemDescription] = useState('');
  const [editingItem, setEditingItem] = useState<ChecklistItem | null>(null);
  
  const { user } = useAppSelector((state) => state.auth);

  const fetchChecklistItems = async () => {
    try {
      setLoading(true);
      const [itemsData, statsData] = await Promise.all([
        checklistService.getChecklistItemsByIssue(issueId),
        checklistService.getChecklistStats(issueId)
      ]);
      setItems(itemsData);
      setStats(statsData);
    } catch (error: any) {
      console.error('Error fetching checklist items:', error);
      setError('Failed to fetch checklist items');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchChecklistItems();
    }
  }, [open, issueId]);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setError(null);
    setNewItemTitle('');
    setNewItemDescription('');
    setEditingItem(null);
    if (onChecklistChange) {
      onChecklistChange();
    }
  };

  const handleAddItem = async () => {
    if (!newItemTitle.trim() || !user) return;

    try {
      const newItem = await checklistService.createChecklistItem({
        title: newItemTitle,
        description: newItemDescription,
        issue: { id: issueId } as any,
        createdBy: user,
        isCompleted: false,
        sortOrder: items.length
      });
      
      setItems([...items, newItem]);
      setStats(prev => ({ ...prev, total: prev.total + 1 }));
      setNewItemTitle('');
      setNewItemDescription('');
    } catch (error: any) {
      console.error('Error creating checklist item:', error);
      setError('Failed to create checklist item');
    }
  };

  const handleToggleItem = async (item: ChecklistItem) => {
    if (!user) return;

    try {
      const updatedItem = await checklistService.toggleChecklistItem(item.id, user.id);
      const updatedItems = items.map(i => i.id === item.id ? updatedItem : i);
      setItems(updatedItems);
      
      // Update stats
      const completedCount = updatedItems.filter(i => i.isCompleted).length;
      setStats(prev => ({ ...prev, completed: completedCount }));
    } catch (error: any) {
      console.error('Error toggling checklist item:', error);
      setError('Failed to update checklist item');
    }
  };

  const handleDeleteItem = async (itemId: number) => {
    try {
      await checklistService.deleteChecklistItem(itemId);
      const updatedItems = items.filter(i => i.id !== itemId);
      setItems(updatedItems);
      
      // Update stats
      const completedCount = updatedItems.filter(i => i.isCompleted).length;
      setStats({ completed: completedCount, total: updatedItems.length });
    } catch (error: any) {
      console.error('Error deleting checklist item:', error);
      setError('Failed to delete checklist item');
    }
  };

  const handleUpdateItem = async () => {
    if (!editingItem || !editingItem.title.trim()) return;

    try {
      const updatedItem = await checklistService.updateChecklistItem(editingItem.id, {
        title: editingItem.title,
        description: editingItem.description
      });
      
      const updatedItems = items.map(i => i.id === editingItem.id ? updatedItem : i);
      setItems(updatedItems);
      setEditingItem(null);
    } catch (error: any) {
      console.error('Error updating checklist item:', error);
      setError('Failed to update checklist item');
    }
  };

  const progressPercentage = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;

  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Tooltip title="Manage Checklist">
          <IconButton
            size="small"
            onClick={handleOpen}
            sx={{
              bgcolor: 'rgba(0,0,0,0.04)',
              '&:hover': { bgcolor: 'rgba(0,0,0,0.08)' }
            }}
          >
            <CheckBoxIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        
        {stats.total > 0 && (
          <Typography variant="body2" color="text.secondary">
            {stats.completed}/{stats.total} completed
          </Typography>
        )}
      </Box>

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h6">Checklist</Typography>
            {stats.total > 0 && (
              <Box sx={{ mt: 1, minWidth: 200 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Typography variant="body2" color="text.secondary">
                    Progress
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {Math.round(progressPercentage)}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={progressPercentage}
                  sx={{ height: 6, borderRadius: 3 }}
                />
              </Box>
            )}
          </Box>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {/* Add new item form */}
          <Box sx={{ mb: 3, p: 2, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 2 }}>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Add New Item
            </Typography>
            <TextField
              fullWidth
              size="small"
              placeholder="Item title"
              value={newItemTitle}
              onChange={(e) => setNewItemTitle(e.target.value)}
              sx={{ mb: 1 }}
            />
            <TextField
              fullWidth
              size="small"
              placeholder="Description (optional)"
              value={newItemDescription}
              onChange={(e) => setNewItemDescription(e.target.value)}
              sx={{ mb: 2 }}
            />
            <Button
              variant="contained"
              size="small"
              startIcon={<AddIcon />}
              onClick={handleAddItem}
              disabled={!newItemTitle.trim()}
            >
              Add Item
            </Button>
          </Box>
          
          <Divider sx={{ mb: 2 }} />
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <List>
              {items.map((item) => (
                <ListItem key={item.id} sx={{ px: 0 }}>
                  <ListItemIcon>
                    <Checkbox
                      checked={item.isCompleted}
                      onChange={() => handleToggleItem(item)}
                      color="primary"
                    />
                  </ListItemIcon>
                  
                  {editingItem?.id === item.id ? (
                    <Box sx={{ flexGrow: 1, mr: 1 }}>
                      <TextField
                        fullWidth
                        size="small"
                        value={editingItem.title}
                        onChange={(e) => setEditingItem({ ...editingItem, title: e.target.value })}
                        sx={{ mb: 1 }}
                      />
                      <TextField
                        fullWidth
                        size="small"
                        value={editingItem.description || ''}
                        onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })}
                        placeholder="Description"
                        sx={{ mb: 1 }}
                      />
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button size="small" onClick={handleUpdateItem}>Save</Button>
                        <Button size="small" onClick={() => setEditingItem(null)}>Cancel</Button>
                      </Box>
                    </Box>
                  ) : (
                    <ListItemText
                      primary={
                        <Typography
                          variant="body2"
                          sx={{
                            textDecoration: item.isCompleted ? 'line-through' : 'none',
                            opacity: item.isCompleted ? 0.7 : 1
                          }}
                        >
                          {item.title}
                        </Typography>
                      }
                      secondary={item.description && (
                        <Typography
                          variant="caption"
                          color="text.secondary"
                          sx={{
                            textDecoration: item.isCompleted ? 'line-through' : 'none',
                            opacity: item.isCompleted ? 0.7 : 1
                          }}
                        >
                          {item.description}
                        </Typography>
                      )}
                    />
                  )}
                  
                  <ListItemSecondaryAction>
                    <IconButton
                      size="small"
                      onClick={() => setEditingItem(item)}
                      sx={{ mr: 1 }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteItem(item.id)}
                      color="error"
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
              
              {items.length === 0 && !loading && (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 3 }}>
                  No checklist items yet. Add your first item above.
                </Typography>
              )}
            </List>
          )}
        </DialogContent>
        
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ChecklistManager;
