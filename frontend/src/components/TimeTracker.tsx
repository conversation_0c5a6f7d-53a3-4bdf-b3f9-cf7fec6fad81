import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Edit,
  Delete,
  Timer,
  AccessTime
} from '@mui/icons-material';
import { format, formatDistanceToNow } from 'date-fns';
import api from '../services/apiService';

interface TimeEntry {
  id: number;
  issue: {
    id: number;
    identifier: string;
    title: string;
  };
  user: {
    id: number;
    username: string;
  };
  activityType: string;
  startTime: string;
  endTime?: string;
  durationMinutes?: number;
  description?: string;
  billable: boolean;
}

interface Issue {
  id: number;
  identifier: string;
  title: string;
}

const ACTIVITY_TYPES = [
  'DEVELOPMENT',
  'TESTING',
  'REVIEW',
  'ANALYSIS',
  'DOCUMENTATION',
  'MEETING',
  'DEBUGGING',
  'DEPLOYMENT',
  'RESEARCH',
  'OTHER'
];

const TimeTracker: React.FC = () => {
  const [activeEntry, setActiveEntry] = useState<TimeEntry | null>(null);
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [issues, setIssues] = useState<Issue[]>([]);
  const [selectedIssue, setSelectedIssue] = useState<number | ''>('');
  const [activityType, setActivityType] = useState<string>('DEVELOPMENT');
  const [description, setDescription] = useState<string>('');
  const [showManualEntry, setShowManualEntry] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Manual entry form state
  const [manualEntry, setManualEntry] = useState({
    issueId: '',
    activityType: 'DEVELOPMENT',
    startTime: '',
    endTime: '',
    description: '',
    billable: true
  });

  useEffect(() => {
    fetchTimeEntries();
    fetchIssues();
    fetchActiveEntry();

    // Update current time every second for active timer display
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const fetchTimeEntries = async () => {
    try {
      const response = await api.get('/api/time-tracking/today');
      setTimeEntries(response.data);
    } catch (err) {
      console.error('Error fetching time entries:', err);
    }
  };

  const fetchIssues = async () => {
    try {
      const response = await api.get('/api/issues?size=100');
      setIssues(response.data.content || []);
    } catch (err) {
      console.error('Error fetching issues:', err);
    }
  };

  const fetchActiveEntry = async () => {
    try {
      const response = await api.get('/api/time-tracking/active');
      setActiveEntry(response.data);
    } catch (err) {
      // No active entry is normal
      setActiveEntry(null);
    }
  };

  const startTimer = async () => {
    if (!selectedIssue) {
      setError('Please select an issue to track time for');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await api.post('/api/time-tracking/start', {
        issueId: selectedIssue,
        activityType,
        description
      });

      setActiveEntry(response.data);
      setDescription('');
      fetchTimeEntries();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to start timer');
    } finally {
      setLoading(false);
    }
  };

  const stopTimer = async () => {
    if (!activeEntry) return;

    try {
      setLoading(true);
      setError(null);

      await api.post(`/api/time-tracking/${activeEntry.id}/stop`);
      setActiveEntry(null);
      fetchTimeEntries();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to stop timer');
    } finally {
      setLoading(false);
    }
  };

  const createManualEntry = async () => {
    try {
      setLoading(true);
      setError(null);

      await api.post('/api/time-tracking/manual', {
        issueId: manualEntry.issueId,
        activityType: manualEntry.activityType,
        startTime: manualEntry.startTime,
        endTime: manualEntry.endTime,
        description: manualEntry.description,
        billable: manualEntry.billable
      });

      setShowManualEntry(false);
      setManualEntry({
        issueId: '',
        activityType: 'DEVELOPMENT',
        startTime: '',
        endTime: '',
        description: '',
        billable: true
      });
      fetchTimeEntries();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create manual entry');
    } finally {
      setLoading(false);
    }
  };

  const deleteEntry = async (entryId: number) => {
    try {
      await api.delete(`/api/time-tracking/${entryId}`);
      fetchTimeEntries();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete entry');
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const getActiveTimerDuration = () => {
    if (!activeEntry) return '0h 0m';
    
    const startTime = new Date(activeEntry.startTime);
    const diffMs = currentTime.getTime() - startTime.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    return formatDuration(diffMinutes);
  };

  const getTotalTimeToday = () => {
    const totalMinutes = timeEntries.reduce((sum, entry) => {
      return sum + (entry.durationMinutes || 0);
    }, 0);
    
    // Add active timer duration
    if (activeEntry) {
      const startTime = new Date(activeEntry.startTime);
      const diffMs = currentTime.getTime() - startTime.getTime();
      const activeMinutes = Math.floor(diffMs / (1000 * 60));
      return formatDuration(totalMinutes + activeMinutes);
    }
    
    return formatDuration(totalMinutes);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Time Tracker
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Active Timer Section */}
      <Paper elevation={2} sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} md={6}>
            <Box display="flex" alignItems="center" gap={2}>
              <Timer color={activeEntry ? 'success' : 'disabled'} />
              <Box>
                <Typography variant="h6">
                  {activeEntry ? 'Timer Running' : 'No Active Timer'}
                </Typography>
                {activeEntry && (
                  <Typography variant="body2" color="textSecondary">
                    {activeEntry.issue.identifier} - {activeEntry.activityType}
                  </Typography>
                )}
              </Box>
            </Box>
          </Grid>
          
          <Grid item xs={12} md={3}>
            <Typography variant="h4" color={activeEntry ? 'success.main' : 'textSecondary'}>
              {getActiveTimerDuration()}
            </Typography>
          </Grid>
          
          <Grid item xs={12} md={3}>
            {activeEntry ? (
              <Button
                variant="contained"
                color="error"
                startIcon={<Stop />}
                onClick={stopTimer}
                disabled={loading}
                fullWidth
              >
                Stop Timer
              </Button>
            ) : (
              <Button
                variant="contained"
                color="success"
                startIcon={<PlayArrow />}
                onClick={startTimer}
                disabled={loading || !selectedIssue}
                fullWidth
              >
                Start Timer
              </Button>
            )}
          </Grid>
        </Grid>

        {!activeEntry && (
          <Box sx={{ mt: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>Issue</InputLabel>
                  <Select
                    value={selectedIssue}
                    onChange={(e) => setSelectedIssue(e.target.value as number)}
                    label="Issue"
                  >
                    {issues.map((issue) => (
                      <MenuItem key={issue.id} value={issue.id}>
                        {issue.identifier} - {issue.title}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Activity</InputLabel>
                  <Select
                    value={activityType}
                    onChange={(e) => setActivityType(e.target.value)}
                    label="Activity"
                  >
                    {ACTIVITY_TYPES.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={5}>
                <TextField
                  fullWidth
                  label="Description (optional)"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="What are you working on?"
                />
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <AccessTime color="primary" />
                <Box>
                  <Typography variant="h6">Today's Total</Typography>
                  <Typography variant="h4" color="primary">
                    {getTotalTimeToday()}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6">Entries Today</Typography>
              <Typography variant="h4" color="secondary">
                {timeEntries.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Box display="flex" gap={1}>
            <Button
              variant="outlined"
              onClick={() => setShowManualEntry(true)}
              fullWidth
            >
              Add Manual Entry
            </Button>
          </Box>
        </Grid>
      </Grid>

      {/* Time Entries List */}
      <Paper elevation={2} sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Today's Time Entries
        </Typography>
        
        {timeEntries.length === 0 ? (
          <Typography color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
            No time entries for today
          </Typography>
        ) : (
          <List>
            {timeEntries.map((entry) => (
              <ListItem key={entry.id} divider>
                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography variant="subtitle1">
                        {entry.issue.identifier}
                      </Typography>
                      <Chip label={entry.activityType} size="small" />
                      {entry.billable && (
                        <Chip label="Billable" size="small" color="success" />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2">
                        {entry.issue.title}
                      </Typography>
                      {entry.description && (
                        <Typography variant="caption" color="textSecondary">
                          {entry.description}
                        </Typography>
                      )}
                      <Typography variant="caption" display="block">
                        {format(new Date(entry.startTime), 'HH:mm')} - 
                        {entry.endTime ? format(new Date(entry.endTime), 'HH:mm') : 'Running'}
                        {entry.durationMinutes && ` (${formatDuration(entry.durationMinutes)})`}
                      </Typography>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={() => deleteEntry(entry.id)}
                    size="small"
                  >
                    <Delete />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </Paper>

      {/* Manual Entry Dialog */}
      <Dialog open={showManualEntry} onClose={() => setShowManualEntry(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Manual Time Entry</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Issue</InputLabel>
                <Select
                  value={manualEntry.issueId}
                  onChange={(e) => setManualEntry({ ...manualEntry, issueId: e.target.value })}
                  label="Issue"
                >
                  {issues.map((issue) => (
                    <MenuItem key={issue.id} value={issue.id}>
                      {issue.identifier} - {issue.title}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Activity Type</InputLabel>
                <Select
                  value={manualEntry.activityType}
                  onChange={(e) => setManualEntry({ ...manualEntry, activityType: e.target.value })}
                  label="Activity Type"
                >
                  {ACTIVITY_TYPES.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Start Time"
                type="datetime-local"
                value={manualEntry.startTime}
                onChange={(e) => setManualEntry({ ...manualEntry, startTime: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="End Time"
                type="datetime-local"
                value={manualEntry.endTime}
                onChange={(e) => setManualEntry({ ...manualEntry, endTime: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={manualEntry.description}
                onChange={(e) => setManualEntry({ ...manualEntry, description: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowManualEntry(false)}>Cancel</Button>
          <Button 
            onClick={createManualEntry} 
            variant="contained"
            disabled={!manualEntry.issueId || !manualEntry.startTime || !manualEntry.endTime}
          >
            Add Entry
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TimeTracker;
