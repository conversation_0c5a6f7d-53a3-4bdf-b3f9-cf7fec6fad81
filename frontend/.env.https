# HTTPS Development Environment Configuration
# This file configures the application for HTTPS development

# API Configuration for HTTPS
VITE_API_HOST=localhost
VITE_API_PORT=8443
VITE_API_BASE_URL=https://localhost:8443/api

# Frontend Configuration
VITE_APP_TITLE=Bug Tracker - Voice AI Assistant
VITE_APP_DESCRIPTION=AI-powered issue tracking with voice assistance

# Voice Assistant Configuration
VITE_VOICE_ENABLED=true
VITE_VOICE_WAKE_WORD_ENABLED=true
VITE_VOICE_DEFAULT_LANGUAGE=en-US

# Development Settings
VITE_DEV_MODE=true
VITE_ENABLE_MOCK_API=false

# Security Settings for HTTPS
VITE_SECURE_CONTEXT=true
VITE_ALLOW_SELF_SIGNED_CERTS=true

# Logging
VITE_LOG_LEVEL=debug
