{"name": "bug-tracking-system-frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "dev:https": "vite --https", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "preview:https": "vite preview --https", "generate-nginx-conf": "node -r ts-node/register scripts/generate-nginx-conf.js", "docker-build": "npm run generate-nginx-conf && npm run build"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.0", "@mui/x-data-grid": "^6.5.0", "@mui/x-date-pickers": "^8.3.0", "@reduxjs/toolkit": "^1.9.5", "@types/lodash": "^4.17.17", "@types/react-grid-layout": "^1.3.5", "axios": "^1.4.0", "date-fns": "^2.30.0", "formik": "^2.2.9", "lodash": "^4.17.21", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-redux": "^8.0.5", "react-router-dom": "^6.11.1", "recharts": "^2.6.2", "xlsx": "^0.18.5", "yup": "^1.1.1"}, "devDependencies": {"@types/node": "^22.15.18", "@types/react": "^18.2.6", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.4", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.40.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "ts-node": "^10.9.1", "typescript": "^5.0.4", "vite": "^4.3.5"}}