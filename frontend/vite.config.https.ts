import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import fs from 'fs'
import path from 'path'

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  const certPath = path.resolve(__dirname, '../certs/murthy.crt')
  const keyPath = path.resolve(__dirname, '../certs/murthy-ca.key')

  return {
    plugins: [react()],
    server: {
      port: 5173,
      host: true,
      https: {
        key: fs.readFileSync(keyPath),
        cert: fs.readFileSync(certPath),
      },
      proxy: {
        '/api': {
          target: 'https://localhost:8443',
          changeOrigin: true,
          secure: false,
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('Proxy error:', err);
            });
          }
        }
      }
    },
    preview: {
      port: 4173,
      host: true,
      https: {
        key: fs.readFileSync(keyPath),
        cert: fs.readFileSync(certPath),
      },
    }
  }
})
