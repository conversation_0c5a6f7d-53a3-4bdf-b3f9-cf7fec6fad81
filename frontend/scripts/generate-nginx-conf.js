/**
 * Generate Nginx Configuration
 *
 * This script generates the nginx.conf file based on the centralized API configuration
 */

const fs = require('fs');
const path = require('path');

// Define API prefix (should match the one in apiConfig.ts)
const API_PREFIX = '/api';

// Define the backend service and port
const BACKEND_SERVICE = process.env.BACKEND_SERVICE || 'backend';
const BACKEND_PORT = process.env.BACKEND_PORT || '8081';
const DOCKER_PROXY_PASS = `http://${BACKEND_SERVICE}:${BACKEND_PORT}`;

const nginxTemplate = `server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;
    }

    location ${API_PREFIX} {
        proxy_pass ${DOCKER_PROXY_PASS};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
`;

// Write the nginx.conf file
fs.writeFileSync(path.join(__dirname, '../nginx.conf'), nginxTemplate);
console.log('Generated nginx.conf with proxy_pass:', DOCKER_PROXY_PASS);
