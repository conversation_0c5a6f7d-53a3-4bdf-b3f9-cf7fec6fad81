// Test script to verify issue creation functionality
// Run this in the browser console to test the form

console.log('Testing Issue Creation Form...');

// Test 1: Check if formik is properly initialized
function testFormikInitialization() {
  console.log('Test 1: Checking formik initialization...');
  
  // Look for the form element
  const form = document.querySelector('form');
  if (!form) {
    console.error('❌ Form element not found');
    return false;
  }
  
  // Check if form fields are present
  const titleField = document.querySelector('#title');
  const descriptionField = document.querySelector('#description');
  const typeField = document.querySelector('#type');
  
  if (!titleField || !descriptionField || !typeField) {
    console.error('❌ Required form fields not found');
    return false;
  }
  
  console.log('✅ Form and required fields found');
  return true;
}

// Test 2: Check for JavaScript errors
function testForErrors() {
  console.log('Test 2: Checking for JavaScript errors...');
  
  // Monitor for errors
  let errorCount = 0;
  const originalError = console.error;
  
  console.error = function(...args) {
    errorCount++;
    originalError.apply(console, args);
  };
  
  // Simulate form interaction
  try {
    const titleField = document.querySelector('#title');
    if (titleField) {
      titleField.focus();
      titleField.value = 'Test Issue';
      titleField.dispatchEvent(new Event('change', { bubbles: true }));
    }
    
    console.log(`✅ Form interaction test completed. Errors: ${errorCount}`);
    return errorCount === 0;
  } catch (error) {
    console.error('❌ Error during form interaction:', error);
    return false;
  } finally {
    console.error = originalError;
  }
}

// Test 3: Check module dropdown functionality
function testModuleDropdown() {
  console.log('Test 3: Testing module dropdown...');
  
  try {
    const moduleSelect = document.querySelector('#moduleId');
    if (!moduleSelect) {
      console.warn('⚠️ Module dropdown not found');
      return true; // Not critical for basic functionality
    }
    
    // Test clicking the dropdown
    moduleSelect.click();
    
    setTimeout(() => {
      const options = document.querySelectorAll('[role="option"]');
      console.log(`✅ Module dropdown has ${options.length} options`);
    }, 100);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing module dropdown:', error);
    return false;
  }
}

// Test 4: Check severity dropdown functionality
function testSeverityDropdown() {
  console.log('Test 4: Testing severity dropdown...');
  
  try {
    const severitySelect = document.querySelector('#severity');
    if (!severitySelect) {
      console.error('❌ Severity dropdown not found');
      return false;
    }
    
    // Test clicking the dropdown
    severitySelect.click();
    
    setTimeout(() => {
      const options = document.querySelectorAll('[role="option"]');
      console.log(`✅ Severity dropdown has ${options.length} options`);
    }, 100);
    
    return true;
  } catch (error) {
    console.error('❌ Error testing severity dropdown:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Issue Creation Form Tests...');
  
  const results = {
    formikInit: testFormikInitialization(),
    errorCheck: testForErrors(),
    moduleDropdown: testModuleDropdown(),
    severityDropdown: testSeverityDropdown()
  };
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Issue creation form is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the errors above.');
  }
  
  return results;
}

// Auto-run tests if this script is executed
if (typeof window !== 'undefined') {
  // Wait for page to load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
  } else {
    setTimeout(runAllTests, 1000); // Give React time to render
  }
}

// Export for manual testing
window.testIssueCreation = runAllTests;
